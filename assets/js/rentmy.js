const { JSDOM } = require('jsdom')
const axios = require("axios");
const utils = require('./utls') 
const moment = require('moment')


class RentMySEO {

  site_specific = null
  google_tracking_id = null

  constructor(config, { key='', cache, storeWiseMeta={} }={}) {
    this.key = key;
    this.config = config;
    this.store_id = config.store_id;
    this.store_name = config.store_name;
    this.store_slug = utils.urlToLabel(config.store_name);
    this.store_logo = '';
    this.access_token = config.access_token;
    this.locationId = config.locationId;

    this.storeWiseMeta = storeWiseMeta;

    this.API_BASE_URL = config?.env?.API_BASE_URL || "https://clientapi.rentmy.co/api/";
    this.ASSET_URL = config?.env?.ASSET_URL || "https://s3.us-east-2.amazonaws.com/images.rentmy.co/";

    this.cache = cache

    this.initHttp()

  }

  initHttp() {
    this.HTTP = axios.create({
      baseURL: this.API_BASE_URL,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Set up interceptors
    this.HTTP.interceptors.request.use((request) => {
      if (this.access_token) {
        request.headers.Authorization = `Bearer ${this.access_token}`;
      }
      if (this.locationId) {
        request.headers.Location = this.locationId;
      }
      return request;
    });
  }

  async getSettings(storeName) {

    if(storeName){
      let result = {}
      let api_url = `/get-settings?store_name=${storeName}`
      let cache_key = `${this.store_id}_${api_url}`
      const cachedData = this.cache.get(api_url);
      if(!cachedData){
        let response = await this.HTTP.get(api_url)
        result = response.data.result
        this.cache.set(cache_key, result, 88000 /** 24.44 hour */);
      } else {
        result = cachedData
      }

      if(result?.error){
        console.error('getSettings__error:: Store not found')
        return null
      }

      let data = result  
      
      this.store_id = data.store.id
      this.store_name = data.store.name
      this.store_slug = data.store.slug
      this.store_logo = data.store.logo
      this.access_token = data.store.token
      this.locationId = data.location.id  
    } 

  }


  async getContents() {

   
    let result = {}
    let api_url = `/contents`
    let cache_key = `${this.store_id}_${api_url}`
    const cachedData = this.cache.get(api_url);
    if(!cachedData){
      let response = await this.HTTP.get(api_url)
      let site_specific = null
      try {
        site_specific = (response?.data?.result?.data || []).find(item => item?.config?.tag === 'site_specific')?.contents
      } catch (error) {
      }
      this.site_specific = site_specific  
      
      this.google_tracking_id = site_specific.confg?.analytics?.google?.tracking_id

      this.cache.set(cache_key, site_specific, 88000 /** 24.44 hour */);

    } else {
      result = cachedData
    }

    if(result?.error){
      console.error('getContents__error:: Store not found')
      return null
    }  

  }


  async getNavigations(req, html, { path }) {

    const navigation_allowed = this?.config?.contents?.navigation //|| !this?.config?.access_token
    if(!navigation_allowed) return html

    let result = {}
    let api_url = `/navigations` 
    let cache_key = `${this.store_id}_${api_url}`
    const cachedData = this.cache.get(api_url);
    if(!cachedData){
      let response = await this.HTTP.get(api_url) 
      if(response.data.status == 'OK'){
        result = response.data.result
        this.cache.set(cache_key, result, 88000 /** 24.44 hour */);
      } 
       
    } else {
      result = cachedData
    } 

    let pages = result.data
    let headerLinks = []
    let footerLinks = []

    if(pages?.length){
      /**
       * Data type of each page
       * {
          id: 15434,
          sequence_no: 1,
          content_id: 0,
          content_type: 'Page',
          label: 'Catalog',
          content_url: 'products-list',
          status: 1,
          type: 'header',
          parent_id: 0,
          children: []
        },
       */
      headerLinks = pages.filter(p => p.type === 'header')
      footerLinks = pages.filter(p => p.type === 'footer')

      const dom = new JSDOM(html);
      const document = dom.window.document;
      const menuElement = document.getElementById('RENTMY_MAIN_MENU'); 
      const pageSlug = req.params.page_slug || '';

      
      let menu_li_items = headerLinks.map(page => {

        let isCurrentpage = (page)=>{
          let match1 = !pageSlug && path == ('/' + page.content_url)
          let match2 = pageSlug && page.content_url.endsWith(pageSlug) 
          return (match1 || match2)
        }
        return (` 
            <li class="nav-item">
                <a href="/${page.content_url}" class="nav-link ${isCurrentpage(page) ? 'active' : ''}">${page.label}</a>
            </li>
          `)
      }).join('')

      menuElement.innerHTML = `
   
        ${menu_li_items}

        <li class="nav-item rm-desktop-search-bar">
            <a href="#" class="nav-link rm-search-bar">
                <i class="bx bx-search"></i>
            </a>
        </li>
        <li class="nav-item rm-desktop-search-bar">
            <a href="#" class="nav-link">
                <i class="bx bx-user"></i>
            </a>
            <ul>
                <li class="RentMy-login-page"><a href="/customer-login">Login</a></li>
                <li class="RentMy-client-portal-page"><a href="/customer-order-history">Order History</a></li>
                <li class="RentMy-client-portal-page"><a href="/customer-profile">Profile</a></li>
                <li class="rentmy_logout_btn hide_if_not_logged_in"><a href="#">Logout</a></li>
            </ul>
        </li>
        <li class="nav-item rm-desktop-cart-bar">
            <a class="rm-search-bar RentMyMiniCart">
            </a>
        </li>
        `

      const fullHTML = dom.serialize();

      return fullHTML 
    } 
    return html
  }

  async setStoreLogo(html) {
    // return html
    const store_name = this?.config?.store_name
    if(!store_name) return html

    const dom = new JSDOM(html);
    const document = dom.window.document;
    const menuElement = document.getElementById('RENTMY_STORE_LOGO'); 

    await this.getSettings(store_name)
    await this.getContents()
    if(menuElement){
      menuElement.src = this.store_logo
    } 
       
    const fullHTML = dom.serialize();

    return fullHTML 
  }


  async getRentmyPageContent(req, route, { HTMLPageContnets='' }={}) { 

     
    let result = {}
    let api_url = ``
    let pageSlug = req.params.page_slug;

    if(route.path == '/about') api_url = '/pages/about'
    else if(route.path == '/contact') api_url = '/pages/contact'
    else if(route.path == '/terms-and-conditions') api_url = '/pages/terms-and-conditions'
    else if(pageSlug){ 
      pageSlug = pageSlug.replace(/\./g, '') 
      api_url = `/pages/${pageSlug}`
    }

    if(!api_url) return {}

    let cache_key = `${this.store_id}_${api_url}`

    const cachedData = this.cache.get(api_url);
    let page_found = true
    if(!cachedData){
      try {
        let response = await this.HTTP.get(api_url) 
        if(response.data.status == 'OK'){
          result = response.data.result
          this.cache.set(cache_key, result, 88000 /** 24.44 hour */);
        } 
      } catch (error) {
        
      }
    } else {
      page_found = false
      result = cachedData
    }

    let data = result?.data || {}
    let dataObject = {
      name: data.name || '',
      title: data.meta_title || '',
      page_title: data?.contents?.heading || '',
      contents: page_found ? (data.contents?.content || '') : '<h2>Page Not Found!</h2>',
      description: data.meta_description || '',
      keywords: data.meta_keyword || '',
      meta_title: data.meta_title || '',
    } 


    if(pageSlug && route.rentmy_page && HTMLPageContnets){
      const dom = new JSDOM(HTMLPageContnets);
      const document = dom.window.document;
      let dynamic_page_title = document.getElementById('dynamic_page_title')
      let breadcrumb_title = document.getElementById('breadcrumb_title_from_page_slug')
      if(dynamic_page_title) {
        dynamic_page_title.innerHTML = dataObject.name
      }
      if(breadcrumb_title) breadcrumb_title.innerHTML = utils.urlToLabel(pageSlug)
      let pageWiseDiv = document.getElementById('in_page_dynamic_contents')
      if(pageWiseDiv){
        pageWiseDiv.innerHTML = dataObject.contents
        let full_html = dom.serialize()
        dataObject.contents =full_html
      }
    }

    return dataObject
  }


  async getBlogPageContent(req, route, { HTMLPageContnets='' }={}) { 

     
    let result = {}
    let limit = req.query.limit || route.limit || 10
    let page_no = req.query.page_no || 1;
    let tags_ = req.query.tags || null;
    const short_description_length = parseInt(route.short_description_length) || 255;
    let api_url = `/blogs`

    let cache_key = `${this.store_id}_${api_url}`
    const cachedData = this.cache.get(api_url);



   
    if(!cachedData){
      try {
        let response = await this.HTTP.get(api_url, { params: {limit, page_no, short_description_length, tags: tags_ }})  
        if(response.data.status == 'OK'){
          result = response.data.result
          this.cache.set(cache_key, result, 600 /** 10 minutes */); 
        } 
      } catch (error) {
      }
    } 

    
    const pagianteData = result
    const blogs = result.data
    const { tags } = await this.getBlogTags() 

    let dataObject = {
      // name: data.name || '',
      // title: data.meta_title || '',
      // page_title: data?.contents?.heading || '',
      contents: '',
      // description: data.meta_description || '',
      // keywords: data.meta_keyword || '',
      // meta_title: data.meta_title || '',
    }  

    const dom = new JSDOM(HTMLPageContnets);
    const document = dom.window.document;
  
    const blog_page_tags = document.querySelector('[selector="blog_page_tags"]');
    
    /** ============================================= */
    /** ================= With tags ================= */
    /** ============================================= */
    if (!tags?.length) {
      if(blog_page_tags) blog_page_tags.remove()
    } else {
      const tagEl = blog_page_tags.querySelector('a[selector="single_tag"]');
  
      if (!tagEl) {
        console.warn('single_tag element not found');
      } else {

        // let TagsIds
        const __tags = String(req.query.tags || '').split(',')
        const tagHTMLs = tags.flatMap((tag, i) => {
          const clonedEl = tagEl.cloneNode(true);

         
          clonedEl.innerHTML = tag.name;
          clonedEl.setAttribute('tag-id', String(tag.id));
          let endPoint = pagianteData.page_no == '1' ? `?tags=${tag.name}` :  `?page_no=${pagianteData.page_no}&tags=${tag.name}`
          clonedEl.href = route.path + endPoint
          if(__tags.includes(tag.name)){
            clonedEl.classList.add('selected')
          }

          if(i == 0){

            const clonedEl___ = tagEl.cloneNode(true);
            clonedEl___.innerHTML = 'All blogs';
            let endPoint = pagianteData.page_no == '1' ? `` :  `?page_no=${pagianteData.page_no}`
            clonedEl___.href = route.path + endPoint 
            if(!tags_){
              clonedEl___.classList.add('selected')
            }

            return [clonedEl___.outerHTML, clonedEl.outerHTML];
          } else {
            return clonedEl.outerHTML;
          }
        });
  
        // Replace existing tags with new ones
        blog_page_tags.innerHTML = tagHTMLs.join(''); 
      }
    }
    /** =============== End With tags =============== */ 


    /** ============================================== */
    /** ================= With Blogs ================= */
    /** ============================================== */

    const blogsArea = document.querySelector('[selector="blogs_contetns"]')
    if(blogsArea){
      const blogEl = blogsArea.querySelector('[selector="single_blog"]')

      if(blogs?.length){
        const blogs_HTML = blogs.map(blog => {
          const clonedEl = blogEl.cloneNode(true); 

          const parent_of_image = clonedEl.querySelector('a:has(img[selector="blog_thumbnail_image"])')
          if(parent_of_image){
            parent_of_image.href = route.path + '/' + blog.slug
          }

          const image_tag = clonedEl.querySelector('img[selector="blog_thumbnail_image"]')
          if(image_tag){
            image_tag.src = blog.thumbnail_image || blog.thumbnail_image || '/image/no-image-available-icon-vector.jpg'
            image_tag.alt = blog.name
          }

          const blog_title = clonedEl.querySelector('a[selector="blog_title"]')
          if(blog_title){
              blog_title.innerHTML = blog.name
              blog_title.href = route.path + '/' + blog.slug
          }
          
          const blog_publish_date = clonedEl.querySelector('[selector="blog_publish_date"]')
          if(blog_publish_date) blog_publish_date.innerHTML = moment(blog.created || new Date()).format(route?.dateFormat || 'MMMM DD, yyy')  
        
          
          const blog_contents = clonedEl.querySelector('[selector="blog_contents"]')
          if(blog_contents) blog_contents.innerHTML = blog.short_description ? (blog.short_description + '...') : blog.short_description
          
          const ready_more = clonedEl.querySelector('a[selector="read_more"]')
          if(ready_more) ready_more.href = route.path + '/' + blog.slug
        
          return clonedEl.outerHTML;
        })
        blogsArea.innerHTML = blogs_HTML.join('')
         
      } else {
        blogsArea.innerHTML = '<h3 class="text-center text-black-50 my-5">No Blogs Found</h3>'
      }
    }
    /** =============== End With blogs =============== */ 


    /** =================================================== */
    /** ================= With Pagiantion ================= */
    /** =================================================== */

  
      let { total, page_no: currentPage, limit: pageLimit } = pagianteData
      const paginattion_area = document.querySelector('[selector="paginattion_area"]')
      if(paginattion_area){
        const previous = paginattion_area.querySelector('a[selector="previous"]')
        const next = paginattion_area.querySelector('a[selector="next"]')

        total = Number(total)
        currentPage = Number(currentPage)
        pageLimit = Number(pageLimit)
        let max_page = Math.ceil((total / pageLimit)) 
        
        if(max_page === 1 || !blogs?.length){
          paginattion_area.remove()
        }

        if(previous){
          if(currentPage == 1){
            previous.removeAttribute('href')
            previous.classList.add('disabled')
          } else {
            previous.href = route.path + '?page_no=' + (currentPage - 1) + (tags_ ? `&tags=${tags_}` : '')
          }
        }
        if(next){ 
          if(currentPage == max_page){
            next.removeAttribute('href')
            next.classList.add('disabled')
          } else {
            next.href = route.path + '?page_no=' + (currentPage + 1) + (tags_ ? `&tags=${tags_}` : '')
          }
        } 
      } 
    /** =============== End With Pagiantion =============== */ 
  
    const full_html = dom.serialize();
    dataObject.contents = full_html; 
    return dataObject
  }


  async getBlogTags() { 

     
    let result = {} 
    let api_url = `/tags?type=blog`

    let cache_key = `${this.store_id}_${api_url}`
    const cachedData = this.cache.get(api_url);
 
   
    if(!cachedData){
      try {
        let response = await this.HTTP.get(api_url)  
        if(response.data.status == 'OK'){
          result = response.data.result
          this.cache.set(cache_key, result, 600 /** 10 minutes */); 
        } 
      } catch (error) {
      }
    } 

    let tags = result.data
  
    return { tags }
  }

  getSubdomain(req){
    if(!req) throw new Error('\n\n\nCustomError:: getStoreName(req), missing req\n\n\n')
    if(this.config.IS_SINGLE_SITE) return ''
    let subDomain = utils.getSubdomain(utils.reqFullUrl(req)) || this.config?.SUBDOMAIN
    // let subDomain = utils.getSubdomain('https://viviangracecreations.com/') || this.config?.SUBDOMAIN
    return subDomain
  }


  getStoreName(req){
    if(!req) throw new Error('\n\n\nCustomError:: getStoreName(req), missing req\n\n\n')
    let storeName = this.getSubdomain(req) || this.store_name
    return storeName
  }

  async getMetaData(req, key, { route }) {

    let store_name = this.getStoreName(req)


    if(!this.config?.IS_SINGLE_SITE){
      await this.getSettings(store_name) 
    }
    
    let metaData = {}
      

    const metaFunction = this[`${key}___meta`];
    if (typeof metaFunction === "function") {
      metaData = await metaFunction.call(this, req);
    } 

    let harcodedMeta = {}
    if(route.meta){
      if(typeof route.meta == 'object'){
        harcodedMeta = route.meta
      }
      else if(typeof route.meta === 'string'){
        harcodedMeta = this.storeWiseMeta?.[store_name]?.[route.meta]
      } 
      else {
        harcodedMeta = {}
      }
    } 


    return {...metaData, ...harcodedMeta} 
    
  }

  reponseisOk(response) {
    return response.status === 200 && response.data.status === "OK";
  }

  productIamge(product_id, product){
    let image = product?.images?.[0]?.['image_small'] || product?.images?.[0]?.['image_large']
    if(!image) return '/image/product-image-placeholder.jpg';
    else return `${this.ASSET_URL}products/${this.store_id}/${product_id}/${image}`
  }

  responseData(response) {
    return response.data.result; // Fixed to properly return the result
  }

   /**
   * Functions according to (page key + '___meta')
   * ==========================================
   */

   async home___meta (req) {  
    try {
      const STORE_NAME = this.getStoreName(req)

      const response = await this.HTTP.get(`/stores/${STORE_NAME}/meta/home`,)

      if (this.reponseisOk(response)) {
          const seo = this.responseData(response)?.data  
          let seoData = {
              title: seo?.title,
              description: seo?.description,
              keywords: seo?.keyword,
              imageUrl: seo?.image,
              image_description: seo?.description,
              favIcon: seo?.favicon,
              twitter: seo?.twitter,
          };
          return seoData
      } else {
          return {};
      }
    } catch (error) {
      console.error("Error fetching product metadata:", error.message)
      return {}; 
    }
  }
 


  async products_list___meta(req, uuid=undefined) { 
    try {
        const categoryId = uuid || req.query?.category
        
        if(categoryId){
          const response = await this.HTTP.get(`get/child-categories/${categoryId}`)
  
          if (this.reponseisOk(response)) {
            const data = this.responseData(response).data
            this['categoryDetails'] = data
            const seo = data.detais.seo
              return {
                  title: seo?.meta_title,
                  description: seo?.meta_description,
              };
          } else {
              return {};
          }
        } else {
          let data = await this.home___meta(req)
          return data
        }
        
    } catch (error) {
      console.error("Error fetching product metadata:", error.message)
      return {}; 
    }
  }


  async product_details___meta(req) {
    return await this.product_details_and_package_details___meta(req)
  }


  async package_details___meta(req) {
    return await this.product_details_and_package_details___meta(req)
  }


  async product_details_and_package_details___meta(req) {
    try {
        const url = req.params?.url ||req.query?.url 
        const storeName = this.getStoreName(req)
        
        
        const response = await this.HTTP.get(`/stores/${storeName}/meta/product-details`, { params: { url }})
        
        if (this.reponseisOk(response)) {
            const seo = this.responseData(response).data
            let data = {
                title: seo?.product_name,
                description: seo?.description,
                keywords: seo?.keyword,
                imageUrl: seo?.image,
                image_description: seo?.description,
                twitter: seo?.twitter,
                sku: seo?.sku,
                price: seo?.rent_price || seo?.buy_price || 0,
                ...(seo?.favicon ? {favIcon: seo?.favicon} : {}),
            };
            return data
        } else {
            return {};
        }
    } catch (error) {
      console.error("Error fetching product metadata:", error.message)
      return {}; 
    }
  }

  async products_list_by_category___meta(req) {
    try {

        const STORE_NAME = this.getStoreName(req)
        const uid = req.params?.uid || req.query?.uid 
        
        if (!uid) {
            console.warn("uid is missing in the request")
            return {};
        }    

        await this.products_list___meta(req, uid) // calling just for breadcrumb data 

        const response = await this.HTTP.get(`/stores/${STORE_NAME}/meta/category?uid=${uid}`, { params: { uid }})

        if (this.reponseisOk(response)) {
            const seo = this.responseData(response)?.data  
            let seoData = {
                title: seo?.category_name,
                description: seo?.description,
                keywords: seo?.keyword,
                imageUrl: seo?.image,
                image_description: seo?.description,
                favIcon: seo?.favicon,
                twitter: seo?.twitter,
            };
            return seoData
        } else {
            return {};
        }
    } catch (error) {
      console.error("Error fetching product metadata:", error)
      return {}; 
    }
  }
 
  async getSitemapData(req, res, {cache}){
    try {

      let cache_key = `${this.store_id}_sitemap_xml_data`
      
      if(req.query.clear === 'this-cache'){
        cache.del(cache_key);
      }
      
      const cachedContent = cache.get(cache_key);
      if(cachedContent){
        res.set('Content-Type', 'text/xml');
        res.send(cachedContent)
        return
      }

      const STORE_NAME = this.getStoreName(req)
      let response = await this.HTTP.get(`/stores/sitemap?store_name=${STORE_NAME}`)
      let url_list = response.data.result?.data?.urls;
      let htmlContent = `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`
      if (url_list) {
        url_list.forEach(url => {
          let fresh_url = url.replace(/&/g, '&amp;')
          let domain_part_pattern = /^https?:\/\/[^/]+/
          fresh_url = fresh_url.replace(domain_part_pattern, utils.reqGetOrigin(req))

          htmlContent += `<url>
            <loc>${fresh_url}</loc>
          </url>`;
        });
      }
      htmlContent += `</urlset>`;

      let one_day = 86400 

      cache.set(cache_key, htmlContent, one_day)

      res.set('Content-Type', 'text/xml');
      res.send(htmlContent);
    } catch (error) {
      console.log(error);

    }
  }
}

module.exports = RentMySEO;
