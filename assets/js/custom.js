$(document).ready(function() {
    $('#gallery-slider').owlCarousel({
      items: 1,
      margin: 10,
      nav:true,
      autoHeight: true
    });
    $('#floral-rental-review-slider').owlCarousel({
        items:1,
        merge:true,
      margin: 10,
      nav:true,
      lool: true,
      autoHeight: true,
      responsive:{
        0:{
            items:1
        },
        576:{
            items:2
        },
        768:{
            items:2
        },
        993:{
            items:3
        },
        1200:{
            items:3
        }
    }
    });
    $('#local-decorclient-slider').owlCarousel({
        items:1,
        merge:true,
      margin: 10,
      nav:true,
      lool: true,
      autoHeight: true,
      responsive:{
        0:{
            items:1
        },
        576:{
            items:2
        },
        768:{
            items:2
        },
        993:{
            items:3
        },
        1200:{
            items:3
        }
    }
    });
})

var owl = $('#customer-slider');
owl.owlCarousel({
    items:1,
    loop:true,
    margin:10,
    autoplay:true,
    autoplayTimeout:1000,
    autoplayHoverPause:true
});
$('.play').on('click',function(){
    owl.trigger('play.owl.autoplay',[1000])
})
$('.stop').on('click',function(){
    owl.trigger('stop.owl.autoplay')
})

// header fixed js
$(document).on("scroll", function() {
    if ($(document).scrollTop() >= 50) {
        $(".rentmy-menu-header").addClass("shrink");
    } else {
        $(".rentmy-menu-header").removeClass("shrink");
    }
});
/*-- active menu js --*/
$(document).ready(function() {
    $('.rentmy-nav-manu ul li a').click(function() {
        $('li a').removeClass("rentmy-active-menu");
        $(this).addClass("rentmy-active-menu");
    });
});
// toogle js 
$(document).ready(function() {
    $(".rentmy-mobile-menubar").click(function() {
        $(".rentmy-nav-manu ").slideToggle(200);
    });

});

// scroll bottom to top 
var btn = $('#scroll-top-button');

$(window).scroll(function() {
  if ($(window).scrollTop() > 300) {
    btn.addClass('show');
  } else {
    btn.removeClass('show');
  }
});

btn.on('click', function(e) {
  e.preventDefault();
  $('html, body').animate({scrollTop:0}, '300');
});

