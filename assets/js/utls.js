const fs = require('fs')
const path = require('path')
const { JSDOM } = require('jsdom')


function reqUrl(req){
    try {
        return req.protocol + '://' + req.get('host');
    } catch (error) {
        return 'error-url'
    }
}
function reqFullUrl(req){
    try {
        let fullUrl = req.protocol + '://' + req.get('host') + req.originalUrl;
        if(fullUrl.match(/clear=(\w+)/)){
          if( fullUrl.match(/\?clear=(\w+)$/) )
            fullUrl = fullUrl.replace(/\?clear=(\w+)$/, '')
          else 
            fullUrl = fullUrl.replace(/clear=(\w+)/, '')
        }
        return fullUrl
    } catch (error) {
        return 'error-url'
    }
}
function reqGetOrigin(req){
    try {
        // return req.protocol + '://' + req.get('host');
        return 'https://' + req.get('host')
    } catch (error) {
        return 'error-url'
    }
}

function setMetaData(html, {
    RentMy,
    req,
    key,
    route={},
    storeName='',
    favIcon='/image/favicon.png',
    title='',
    og_title= '', // if empty, title will be set
    og_site_name='', // if empty, title will be set
    og_url='',
    description='',
    keywords='',
    author='RentMy',
    imageUrl='',
    req_full_url='', 
    twitter='',
    sku='',
    price='',
  }={}){
    const dom = new JSDOM(html);
    const document = dom.window.document;
    const headElement = document.querySelector('head'); 

    const theTitle = (og_title='')=>{ 
      let titleText = og_title || title || route.title 
      if(titleText.includes('::')) titleText = titleText.split('::')[1]
      else if(titleText.includes('||')) titleText = titleText.split('|')[1]
      else if(titleText.includes('|')) titleText = titleText.split('|')[1]
      titleText = titleText.replace(/\s\s/g, '')
      return [RentMy.store_slug, titleText].join(' :: ')
    }
  
    if(headElement){

      let PRODUCT_PAGE_KEYS = ['product_details', 'package_details', 'products_list', 'products_list_by_category']

      let type = PRODUCT_PAGE_KEYS.includes(key) ? 'Product' : 'WebPage'

      setFavicon(headElement, favIcon)
      setCanonical(document, headElement, req)      
      setTitle(document, headElement, theTitle())      
      insertMeta(document, headElement, { property: 'description', content: description, propertyName: 'name'}) 
      insertMeta(document, headElement, { property: 'keywords', content: keywords, propertyName: 'name'})   
      
      insertMeta(document, headElement, { property: 'twitter:card', content: 'summary', propertyName: 'name'})  
      insertMeta(document, headElement, { property: 'twitter:description', content: description, propertyName: 'name'})  
      insertMeta(document, headElement, { property: 'twitter:site', content: twitter, propertyName: 'name'})  
      insertMeta(document, headElement, { property: 'twitter:title', content: og_title || theTitle(), propertyName: 'name'})  
      insertMeta(document, headElement, { property: 'twitter:image', content: imageUrl || RentMy.store_logo, propertyName: 'name'})  
      insertMeta(document, headElement, { property: 'twitter:image:alt', content: og_title || theTitle(), propertyName: 'name'})  
      

      insertMeta(document, headElement, { property: 'og:title', content: og_title || theTitle()})  
      insertMeta(document, headElement, { property: 'og:site_name', content: og_site_name || theTitle()})  
      insertMeta(document, headElement, { property: 'og:url', content: og_url || reqFullUrl(req)})  
      insertMeta(document, headElement, { property: 'og:type', content: type})  
      insertMeta(document, headElement, { property: 'og:description', content: description})  
      insertMeta(document, headElement, { property: 'og:author', content: author})
      insertMeta(document, headElement, { property: 'og:image', content: imageUrl || RentMy.store_logo})
      insertMeta(document, headElement, { property: 'og:image:alt', content: og_title || title})
      insertMeta(document, headElement, { property: 'og:locale', content: 'en_US'})
  
      // Fixe meta items
      insertMeta(document, headElement, { property: 'og:robots', content: 'index, follow'})   




      /**
       * ==== Why is Structured Data Important? ====
       * 1) Improves SEO (Search Engine Optimization)
       *    a) Search engines like Google use structured data to better understand the content on a page
       *    b) This helps your page appear in Rich Results or Rich Snippets, which are enhanced search results with additional visual or contextual information
       * 2) Enables Rich Snippets
       *    a) Rich snippets include things like page ratings, authorship, images, breadcrumbs, FAQs, or events directly in search results.
       *    ::Example
       *           i. A product page could show star ratings, price, and availability.
       *           ii. An article could display the author, publish date, and featured image.   
       * 3) Machine Readable Data:
       *    a) The script uses application/ld+json, a JSON-LD format recommended by Google for embedding structured data.
       *    b) Machines (like search engine crawlers) can easily read this format.
       */

      const OFFER_BOJECT = {
        "offers": {
          "@type": "Offer",
          "url": "",
          "price": price,
          "priceCurrency": "",
          "priceValidUntil": "",
          "availability": "",
          "itemCondition": "",
          "shippingDetails": {
            "@type": "OfferShippingDetails",
            "shippingRate": {
              "@type": "MonetaryAmount",
              "value": "",
              "currency": ""
            },
            "shippingDestination": {
              "@type": "DefinedRegion",
              "addressCountry": ""
            },
            "deliveryTime": {
              "@type": "ShippingDeliveryTime",
              "handlingTime": {
                "@type": "QuantitativeValue",
                "minValue": "",
                "maxValue": "",
                "unitCode": "DAY"
              },
              "transitTime": {
                "@type": "QuantitativeValue",
                "minValue": "",
                "maxValue": "",
                "unitCode": "DAY"
              }
            }
          }
        }
      }

      const BRAND = {
        "brand": {
          "@type": "Brand",
          "name": RentMy.store_slug
        },
      }


      const SEO_JSON = {
        "@context": "https://schema.org/",
        "@type": type,
        "name": title,
        "image": imageUrl,
        "url": req_full_url,
        "description": description,
        "sku": sku,
        "mpn": "", 
        // "material": "",
        ...(PRODUCT_PAGE_KEYS.includes(key) ? { color: "Red, Black, White" } : {}),
        ...(PRODUCT_PAGE_KEYS.includes(key) ? { size: "SM, XL, XXL" } : {}),
        ...(PRODUCT_PAGE_KEYS.includes(key) ? BRAND : {}),
        
        "review": {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "4.5",
            "bestRating": "5"
          },
          "author": {
            "@type": "Person",
            "name": RentMy.store_slug
          },
          "reviewBody": "",
          "datePublished": ""
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.5",
          "reviewCount": "123"
        },
        ...(PRODUCT_PAGE_KEYS.includes(key) ? OFFER_BOJECT : {})
        
      }


      let structurdSeoData = `
      <script type="application/ld+json">
        ${JSON.stringify(SEO_JSON)}
      </script>
      `

 

        
      // Ensure compatibility with older browsers
      structurdSeoData += `
        <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
      `

      headElement.insertAdjacentHTML("beforeend", structurdSeoData);
    }  
  
    const fullHTML = dom.serialize();
  
    return fullHTML;
  
  }


function setCdnUrls(html, config, { key, route}){
  const dom = new JSDOM(html);
  const document = dom.window.document;
  const headElement = document.querySelector('head');
  const rentmy_page_contents = document.querySelector('#dynamic_page_contents');
  if(rentmy_page_contents){
    rentmy_page_contents.setAttribute('page', key)
    rentmy_page_contents.setAttribute('page-slug', route.path)
  }

  if(headElement){
    let DATEPICKER_CSS_URL = config?.env?.DATEPICKER_CSS_URL || '/css/em-datetimepicker.min.css' 
    headElement.insertAdjacentHTML("afterbegin", `      
      <link rel="stylesheet" href="${DATEPICKER_CSS_URL}">
      `) 
    
    if(route.path !== '/make-token'){
      let DATEPICKER_SCRIPT_URL = config?.env?.DATEPICKER_SCRIPT_URL || '/js/em-datetimepicker.min.js' 
      headElement.insertAdjacentHTML("afterbegin", `      
        <script src="${DATEPICKER_SCRIPT_URL}"></script>
      `) 
    }

    const cssElement = headElement.querySelector('link[CDN_CSS]');
    if(cssElement) {
      cssElement.href = config.env?.CSS_URL || '/css/index.css';
    } 
    if(route.path !== '/make-token'){
      const scriptElement = headElement.querySelector('script[CDN_SCRIPT]');
      if(scriptElement) {
        scriptElement.defer = true;
        scriptElement.src = config.env?.SCRIPT_URL || '/js/script_prod.js'; 
      }
    }


  }  
  const fullHTML = dom.serialize();  
  return fullHTML;  
}

function urlToLabel(url) {  
  let text = url
  if(!text) return '';
  text = String(text);
  const ucFirst = (str) => (str.charAt(0).toUpperCase() + str.slice(1).toLowerCase());
  text = text.replace(/_/g, '-')
  let final_text = text.split('-').map(ucFirst).join(' ');
  return final_text
}


async function modifyTheHTML(html, config, { key, route, req, RentMy}){
  const dom = new JSDOM(html);
  const document = dom.window.document;

  if(key === 'products_list_by_category'){
    const rentmybreadcrumbtitle = document.querySelector('[rentmybreadcrumbtitle="categoryProduct"]');
    const rentmybreadcrumbtitle2 = document.querySelector('[rentmybreadcrumbtitle="subcategoryProduct"]');
    if(rentmybreadcrumbtitle && rentmybreadcrumbtitle2){
      // let url = req.params.url
      // rentmybreadcrumbtitle.innerHTML = urlToLabel(url)  

      if(RentMy.categoryDetails?.path?.length){
        let paths = RentMy.categoryDetails.path.slice(-2)
        let [cat1, cat2] = paths
        if(paths.length == 2){
          let page_url = route.path.replace(':uid', cat1.url)
          page_url = page_url.replace(':url', req.params.url)

          rentmybreadcrumbtitle.innerHTML = `<a href="${page_url}">${cat1.name}</a>`
          rentmybreadcrumbtitle2.innerHTML = cat2.name 
        } else {
          rentmybreadcrumbtitle.innerHTML = cat1.name 
        }
      }
    } 

  }

  const fullHTML = dom.serialize();  
  return fullHTML;  
}


function setFavicon(headElement, icon) {
  let favIconEl
  favIconEl = headElement.querySelector('[rel="shortcut icon"]')
  if(favIconEl) favIconEl.remove();

  favIconEl = headElement.querySelector('[rel="icon"]')
  if(favIconEl) favIconEl.remove();

  headElement.insertAdjacentHTML("afterbegin", `<link rel="icon" href="${icon}" type="image/x-icon">`);

}

function setTitle(document, headElement, title) {
  let titleTag = headElement.querySelector("title");
  if (titleTag) titleTag.textContent = title || 'RentMy Service';
  else {
    let titleTag = document.createElement("title");
    titleTag.textContent = title || 'RentMy Service';
    headElement.insertAdjacentElement("afterbegin", titleTag);
  }
}
function setCanonical(document, headElement, req) { 
  let linkTag = document.createElement("link");
  linkTag.setAttribute('rel', 'canonical')
  linkTag.setAttribute('href', reqFullUrl(req))
  headElement.insertAdjacentElement("afterbegin", linkTag);
   
}

function insertMeta(document, headElement, { propertyName='property', property = "og:title", content = "" }) {
  let metaTag = headElement.querySelector(`meta[property="${property}"]`);
  let position = property === "og:title" ? 'beforebegin' : 'afterend'
  if (metaTag) {
    metaTag.setAttribute(propertyName, property);
    metaTag.setAttribute("content", content || "");
  } else {
    let metaTag = document.createElement("meta");
    metaTag.setAttribute(propertyName, property);
    metaTag.setAttribute("content", content || "");
    headElement.appendChild(metaTag);
    if(metaTag){
      if(property === "og:title") metaTag.insertAdjacentHTML('beforebegin', '<!--=og:title=-->')
      else metaTag.insertAdjacentHTML('afterend', '<!--=-->')
    }
  }
}


function addTagManagerCode(trackingCode, document){
  if(trackingCode && trackingCode?.includes('GTM-')) {
    // Inject GTM script in <head>
    const head = document.getElementsByTagName("head")[0];
    const headerScript = document.createElement("script");
    headerScript.type = "text/javascript";
    headerScript.textContent = `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});
      var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
      j.async=true;j.src= 'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); 
      })(window,document,'script','dataLayer','${trackingCode}');
    `;
    if(head) head.appendChild(headerScript)

    // Inject <noscript> iframe in <body>
    const body = document.getElementsByTagName("body")[0];
    const noscript = document.createElement("noscript");
    noscript.innerHTML = `
      <iframe src="https://www.googletagmanager.com/ns.html?id=${trackingCode}"
      height="0" width="0" style="display:none;visibility:hidden"></iframe>
    `;
    if(body) body.appendChild(noscript);
  }
}

function addGoogleAnaliticsCode(trackingCode, document){
  if(trackingCode && (trackingCode?.includes('G-') || trackingCode?.includes('UA-'))) {
    const head = document.getElementsByTagName("head")[0];
    const topScript = document.createElement("script");
    topScript.type = "text/javascript";
    topScript.src = `//www.googletagmanager.com/gtag/js?id=${trackingCode}`;
    topScript.async = true;
    head.appendChild(topScript);
    const js = `
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());
      ${trackingCode.slice(0, 2) == 'UA' ? '' : `gtag('config', '${trackingCode}');` }`;
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.appendChild(document.createTextNode(js));
    head.appendChild(script);
    console.log('added GA script');
  }
}


function setContents(html, HTMLPageContnets, selector, { req, RentMy }){
  html = html.replace(new RegExp('<!--=og:title=-->', 'g'), '\n\t\t')
  html = html.replace(new RegExp('<!--=-->', 'g'), '\n\t\t')
  const dom = new JSDOM(html);
  const document = dom.window.document;
  const targetElement = document.querySelector(selector)  

  let trackingCode = 'GTM-5PC4PCCZ' || RentMy.google_tracking_id

  addTagManagerCode(trackingCode, document)
  addGoogleAnaliticsCode(trackingCode, document)

  if(targetElement){ 
    targetElement.innerHTML = HTMLPageContnets
  }  
  const fullHTML = dom.serialize()
  return fullHTML
}


function getSubdomain(fullUrl='') { 

  let url = fullUrl
  let subDomain = ""; 

  if(fullUrl.startsWith('http')){
      url = url.replace('https://www.', '') 
      url = url.replace('http://www.', '') 

      url = url.replace('https://', '')
      url = url.replace('http://', '')

      let parts = url.split('.')

      subDomain = (parts[0] + '.' + parts[1]).replace(/\//g, '').replace(/undefined/g, '')

      if(subDomain.startsWith('localhost')){
        subDomain = ''
      }
      
  } else {
      subDomain = '' //invalidurl
  }

  return subDomain
}

module.exports = {
    reqUrl,
    reqFullUrl,
    reqGetOrigin,
    setMetaData,
    setCdnUrls,
    setContents,
    getSubdomain,
    modifyTheHTML,
    urlToLabel,
};
