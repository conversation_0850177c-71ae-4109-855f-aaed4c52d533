/**
 * It will be load on web
 */ 

document.addEventListener("DOMContentLoaded", function(){

    displayRentmyConfig()
    applyCopyToClicpboard() 
    boundAccessTokenForm()
})

function boundAccessTokenForm(){

    let API_BASE_URL = (RENTMY_GLOBAL?.env?.RENTMY_GLOBAL || 'https://clientapi.rentmy.co/api/')
    let API_URL = API_BASE_URL + 'apps/access-token'

    let FORM_AREA = document.getElementById('FORM_AREA') 
    let RRENTMY_LOADER = document.getElementById('RRENTMY_LOADER') 

    const getToken = (payload) => {
        fetch(API_URL,
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                method: "POST",
                body: JSON.stringify(payload)
            })
            .then(async function(response){  
                let res = await response.json()
                let ERROR_RESPONSE = document.getElementById('ERROR_RESPONSE')
                
                if(res.status == 'OK'){
                    FORM_AREA.hidden = false
                    ERROR_RESPONSE.hidden = true
                    let data = res.result.data
                    displayRentmyConfig({
                        store_name: data.store_name,
                        store_id: data.store_id,
                        locationId: data.location_id,
                        access_token: data.token,
                    })
                } else {
                    FORM_AREA.hidden = true
                    ERROR_RESPONSE.textContent = res?.result?.message
                    ERROR_RESPONSE.hidden = false
                }
             })
            .catch(function(response){ 
                console.error(response)
             })
             .finally(()=> RRENTMY_LOADER.hidden = true)
    }


    let form = document.getElementById('RENTMY_TOKEN_FORM')

    form.addEventListener('submit', function(e){
        e.preventDefault()
        e.stopPropagation()
        let { api_key, api_secret } = e.target 

        let isValid = true

        if(!api_key.value){
            api_key.classList.add('invalid')
            isValid = false
        } 

        if(!api_secret.value){
            api_secret.classList.add('invalid')
            isValid = false
        }  

        if(!isValid) return

        api_key.classList.remove('invalid')
        api_secret.classList.remove('invalid')

        let payload = {
            api_key: api_key.value,
            api_secret: api_secret.value,
        } 
        RRENTMY_LOADER.hidden = false
        getToken(payload)
    })
}



// Function to syntax highlight JSON
function syntaxHighlight(json) {
    if (typeof json != 'string') {
        json = JSON.stringify(json, null, 2);
    }
    
    // First escape HTML special chars
    json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    
    // Then apply syntax highlighting
    let json_filterd = json.replace(
        /("(\\["\\/bfnrt]|\\u[a-fA-F0-9]{4})*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, 
        function (match) {
            let cls = 'number';
            if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                    cls = 'key';
                } else {
                    cls = 'string';
                }
            } else if (/true|false/.test(match)) {
                cls = 'boolean';
            } else if (/null/.test(match)) {
                cls = 'null';
            }
            return '<span class="' + cls + '">' + match + '</span>';
        }
    );

    json_filterd = json_filterd.replace(new RegExp('"IS_SINGLE_SITE": <span class="boolean">true</span>', 'g'), '"IS_SINGLE_SITE": IS_SINGLE_SITE')
    json_filterd = json_filterd.replace(new RegExp('"SUBDOMAIN": <span class="string">""</span>', 'g'), '"SUBDOMAIN": SUBDOMAIN')


    let final_content = [
        `var SUBDOMAIN = ''<br>`,
        `var IS_SINGLE_SITE = true<br>`,
        `var DOMAIN = 'WILL_SET_WITH_NODE'<br><br>`,
        `<span style="font-size: 17px;">var</span>&nbsp;`,
        `<span style="font-size: 13px;">RENTMY_GLOBAL = </span>`,
        json_filterd,
        `<br><br>`,
        `module.exports = RENTMY_GLOBAL`,
    ] 


    return final_content.join('')
}


function displayRentmyConfig({ store_id='', locationId='', store_name='', access_token='' }={}){
    const jsonDisplay = document.getElementById('json-display');
    let CLONED_RENTMY_GLOBAL = JSON.parse(JSON.stringify(RENTMY_GLOBAL))

    CLONED_RENTMY_GLOBAL['store_id'] = store_id
    CLONED_RENTMY_GLOBAL['locationId'] = locationId
    CLONED_RENTMY_GLOBAL['store_name'] = store_name
    CLONED_RENTMY_GLOBAL['access_token'] = access_token 

    delete CLONED_RENTMY_GLOBAL.images
    delete CLONED_RENTMY_GLOBAL.wp_current_user
    if(CLONED_RENTMY_GLOBAL.env){
        delete CLONED_RENTMY_GLOBAL.env.PORT
        delete CLONED_RENTMY_GLOBAL.env.API_BASE_URL
        delete CLONED_RENTMY_GLOBAL.env.ASSET_URL
        delete CLONED_RENTMY_GLOBAL.env.PAYMENT_DOMAIN
    }
    
    if(CLONED_RENTMY_GLOBAL?.home_url){
        CLONED_RENTMY_GLOBAL['home_url'] = String(CLONED_RENTMY_GLOBAL['home_url']).replace(DOMAIN, '')
        if(!CLONED_RENTMY_GLOBAL['home_url']) CLONED_RENTMY_GLOBAL['home_url'] = '/'
    }

    if(CLONED_RENTMY_GLOBAL?.page){
        Object.keys(CLONED_RENTMY_GLOBAL.page).forEach(key => {
            CLONED_RENTMY_GLOBAL.page[key] = String(CLONED_RENTMY_GLOBAL.page[key]).replace(DOMAIN, '')
            if(!CLONED_RENTMY_GLOBAL.page[key].startsWith('/')) CLONED_RENTMY_GLOBAL.page[key] = '/' + CLONED_RENTMY_GLOBAL.page[key]
        }) 
    }


    jsonDisplay.innerHTML = syntaxHighlight(CLONED_RENTMY_GLOBAL); 
}


function applyCopyToClicpboard(){
    const jsonDisplay = document.getElementById('json-display');  

    const copyBtn = document.getElementById('copy-btn');
    const notification = document.getElementById('notification');

    copyBtn.addEventListener('click', function() {
        // Make the element editable to enable selection
        jsonDisplay.contentEditable = true;
        
        // Select all content
        const range = document.createRange();
        range.selectNodeContents(jsonDisplay);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        
        try {
            // Execute copy command
            document.execCommand('copy');
            
            // Show success notification
            notification.classList.add('show');
            setTimeout(function() {
                notification.classList.remove('show');
            }, 2000);
        } catch (err) {
            console.error('Copy failed:', err);
            
            // Fallback to modern Clipboard API if execCommand fails
            navigator.clipboard.writeText(JSON.stringify(RENTMY_GLOBAL, null, 2))
                .then(() => {
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2000);
                })
                .catch(err => console.error('Clipboard API failed:', err));
        } finally { 
            jsonDisplay.contentEditable = false; 
            selection.removeAllRanges();
        }
    });
    
}
