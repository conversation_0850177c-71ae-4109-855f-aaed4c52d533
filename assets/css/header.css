
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&family=Raleway:ital,wght@0,100..900;1,100..900&display=swap');
/*====================== 
Variable css 
========================*/
:root {
    
    --bg-primary: #e2a173;
    --bg-secondary: #516776;
    --bg-tertiary: #000000;

    --font-primary: #e2a173;
    --font-secondary: #516776;
    --font-tertiary: #000000;
}

* {
  font-family: "Raleway", sans-serif;
}
body {
  font-family: "Raleway", sans-serif;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Crimson Text", serif;
  color: #343e4c;
  font-weight: 600;
}
span,
b,
p,
strong,
em,
label,
input,
textarea,
button,
select,
ul li,
ol li,
ul li a,
sub,
sup,
samp,
.form-control,
input,
select,
table tr th,
table tr td,
button,
a {
  font-family: "Raleway", sans-serif;
}
p {
    color: #343434;
    font-weight: 400;
    font-size: 17px;
    line-height: 1.6;
    
}
h1 {
 font-size: 70px;
}
h2 {
  font-size: 50px;
}
h3 {
  font-size: 26px;
}
h4 {
  font-size: 20px;
}
.fs-32 {
  font-size: 32px;
}
a, 
.page-title {
  color: #5e6c68f2;
  text-decoration: none;
}
a:hover, 
.page-title:hover {
  color: #ae6a45;
}
.uabb-second-heading-text {
  font-family: 'Amertha', sans-serif;
  color: rgba(94, 108, 104, 0.95);
  font-weight: 500;
}
.fl-button,
.vivian-btn {
  display: inline-block;
    border-style: solid;
    border-width: 0;
    background-clip: border-box;
    background-color: rgba(94, 108, 104, 0.95);
    padding: 14px 30px;
    font-weight: 600;
    color: #fff;
    text-decoration: none;
    margin-right: 15px;
    font-size: 16px;
    border: none;
}
.vivian-btn-bg {
  background-color: #a96641;
}
.vivian-btn-navyblue {
  background-color: #343e4c;
}
.fl-button {
  background-color: #343e4c;
  margin-left: 30px;
  color: #fff;
}
.fl-button span {
  color: #fff;
}
.vivian-btn:hover {
  background-color: #343e4c;
  color: #fff;
}
.vivian-btn.vivian-btn-navyblue:hover {
  background-color: #a96641;;
}
@media (min-width: 1500px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 1456px;
    }
}
@media (max-width: 1450px) {
  h1 {
    font-size: 60px;
  }
  h2 {
    font-size: 45px;
  }
  h3 {
    font-size: 22px;
  }
  h4 {
    font-size: 18px;
  }
  .fs-32 {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  h1 {
    font-size: 50px;
  }
  h2 {
    font-size: 40px;
  }
  h3 {
    font-size: 22px;
  }
  h4 {
    font-size: 18px;
  }
  .vivian-btn {
    font-size: 13px;
    padding: 10px 15px;
  }
  .fs-32 {
    font-size: 25px;
  }
  p {
    font-size: 16px;
  }
}
@media (max-width: 575px) {
  h1 {
    font-size: 35px;
  }
  h2 {
    font-size: 30px;
  }
  h3 {
    font-size: 18px;
  }
  h4 {
    font-size: 16px;
  }
  .fs-32 {
    font-size: 20px;
  }
  .vivian-btn {
    margin-right: 0;
  }
}

/*======================== 
Header area  
==========================*/
.rentmy-header {
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.03);
}
.rentmy-header .rentmy-top-header {
  background-color: var(--bgcolor-secondary);
  width: 100%;
  height: auto;
  padding-bottom: 0;
  position: relative;
  z-index: 999;
}
.rentmy-header .rentmy-top-header .row {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.rentmy-header .rentmy-top-header .header-top-left,
.rentmy-header .rentmy-top-header .header-top-right {
  display: flex;
  height: 50px;
  width: unset;
  align-items: center;
  justify-content: flex-end;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu {
  height: 100%;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul {
  height: 100%;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li {
  height: 100%;
  margin-left: 0;
  display: inline-block;
  position: relative;
  border-right: 1px solid #3f4750;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li a,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li a {
  -webkit-transition: 0.3s;
  transition: 0.3s;
  font-size: 15px;
  font-weight: 400;
  color: #fff;
  line-height: 50px;
  display: block;
  padding: 0 10px;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li a:hover,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li a:hover {
  color: var(--fontcolor-primary);
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li a i,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li a i {
  padding-right: 3px;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li:last-child,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li:last-child {
  border-right: none;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li.header-top-loginregister,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li.header-top-loginregister {
  cursor: pointer;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li.header-top-loginregister a,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li.header-top-loginregister a {
  padding: 0 20px;
  color: #fff;
  font-weight: 500;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li.header-top-loginregister a:hover,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li.header-top-loginregister a:hover {
  color: var(--fontcolor-primary);
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li.header-top-loginregister a i,
.rentmy-header .rentmy-top-header .header-top-right .rentmy-top-menu ul li.header-top-loginregister a i {
  font-weight: 600;
}
.rentmy-header .rentmy-top-header .header-top-right {
  padding-right: 15px;
}
.rentmy-header .rentmy-top-header .header-top-left {
  padding-left: 15px;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li {
  margin-left: 0;
}
.rentmy-header .rentmy-top-header .header-top-left .rentmy-top-menu ul li a {
  display: flex;
  align-items: center;
}
.rentmy-header .rentmy-menu-header {
  background-color: #fff;
  width: 100%;
  height: 100px;
  transition: all 0.4s ease-in-out;
  z-index: 99;
  top: 0;
  position: fixed;
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.03);
}
.rentmy-header .rentmy-menu-header .row {
  justify-content: space-between;
}
.rentmy-header .rentmy-menu-header .rentmy-logo {
  width: 200px;
}
.rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area {
  height: 100px;
  padding-left: 15px;
}
.rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner {
  display: table;
  table-layout: fixed;
  width: auto;
  height: 100%;
}
.rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle {
  display: table-cell;
  height: 40px;
  vertical-align: middle;
  transition: all 0.4s ease-in-out;
}
.rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a {
  display: block;
  height: 40px;
}
.rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a img {
  width: unset;
  max-width: unset;
  height: 40px;
  transition: all 0.4s ease-in-out;
}
.rentmy-menu-area {
  width: max-content;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu {
  padding-right: 15px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-mobile-device {
  display: none;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu {
  display: inline-block;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  height: 100px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li {
  display: inline-block;
  position: relative;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a {
  padding: 0 10px;
  display: block;
  font-weight: 400;
  font-size: 15px;
  line-height: 100px;
  transition: all 0.4s ease-in-out;
  color: #2f394e;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a i {
  font-size: 14px;
  margin-left: 3px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a .lni-chevron-down {
  font-size: 9px;
  margin-left: 3px;
  font-weight: bold;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a:hover {
  color: #af6137;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a.rentmy-active-menu {
  color: #af6137;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li:last-child a {
  padding-right: 0;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li .nav-submenu {
  position: absolute;
  min-width: 320px;
  height: auto;
  padding: 20px 0px;
  background-color: #fff;
  transform: perspective(600px) rotateX(-90deg);
  transform-origin: 0 0 0;
  left: 0;
  right: auto;
  opacity: 0;
  visibility: hidden;
  transition: 0.5s;
  z-index: 99;
  top: 100%;
  text-align: left;
  border-top: 2px solid #9c5a36;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li .nav-submenu.nav-submenu-large {
  min-width: 800px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li ul li {
  width: 100%;
  display: inline-block;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li ul li a {
  display: block;
  padding: 14px 20px;
  line-height: 20px;
  border-bottom: 1px solid var(--linecolor-light-primary);
  color: var(--fontcolor-secondary);
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li ul li a:hover {
  color: var(--fontcolor-primary);
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li ul li:last-child a {
  border-bottom: none;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li:hover .nav-submenu {
  visibility: visible;
  opacity: 1;
  transform: perspective(600px) rotateX(0);
}
.nav-submenu ul {
  height: auto !important;
  padding: 0;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area.rentmy-rightside-menu .rentmy-menu .rentmy-nav-manu ul li {
  padding-left: 0px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li.rm-header-booknow-btn a {
  background-color: #5e6c68f2;
  color: #fff;
  width: unset;
  height: unset;
  padding: 10px 20px;
  line-height: unset;
  text-align: center;
  border-radius: 0;
  border: none;
  font-weight: 500;
  font-size: 15px;
  margin-left: 30px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li.rm-header-booknow-btn a:hover {
  background-color: #9c5a36;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area.rentmy-rightside-menu .rentmy-menu .rentmy-nav-manu ul li.rm-header-booknow-btn a:hover {
  background-color: #313a45;
}
.rm-desktop-user-bar i,
.rm-cart-bar i {
  font-size: 22px !important;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area.rentmy-rightside-menu .rentmy-menu .rentmy-nav-manu ul li a i {
  font-size: 18px;
}
.rentmy-header .rentmy-menu-header .rentmy-menu-area.rentmy-rightside-menu .rentmy-menu .rentmy-nav-manu ul li a .rm-cart-count-circle {
  width: 18px;
  height: 18px;
  border-radius: 100px;
  background-color: #3ac4fa;
  color: #fff;
  position: absolute;
  top: 4px;
  right: 2px;
  font-size: 8px;
  line-height: 18px;
}
.rentmy-header .rentmy-menu-header.shrink {
  top: 0;
}
.rentmy-header .rentmy-menu-header.shrink .rentmy-nav-manu ul li a {
  padding: 0 15px;
  line-height: 100px;
}
.rm-search-bar,
.rm-user-bar {
  border: 1px solid #c77a63 !important;
  line-height: 40px !important;
  border-radius: 100px;
  color: #c77a63 !important;
  display: inline-flex !important;
  width: 45px;
  height: 45px;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.rm-search-bar:hover,
.rm-user-bar:hover {
  background-color: var(--bgcolor-primary);
  border: 1px solid var(--linecolor-primary) !important;
  color: var(--fontcolor-white) !important;
}

@media (max-width: 1450px) {
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li.rm-header-booknow-btn a {
    display: none;
  }
}
@media (max-width: 1199px) {
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle {
      height: 50px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a {
      height: 50px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a img {
      height: 50px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a {
      font-size: 16px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a i {
      font-size: 18px;
  }
}

@media (min-width: 992px) {
  .rentmy-header .rentmy-menu-header .rentmy-menu .rentmy-nav-manu {
      display: inline-block !important;
  }
}

@media (max-width: 991px) {
  .rentmy-header .rentmy-top-header {
      display: none;
  }
  .rentmy-header .rentmy-menu-header {
      height: 70px;
      top: 0;
  }
  .rentmy-header .rentmy-menu-header .container {
      background-color: transparent;
  }
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area {
      height: 70px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle {
      height: 25px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a {
      height: 25px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-logo .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a img {
      height: 25px !important;
      width: auto !important;
      max-width: unset !important;
  }
  .rentmy-menu-area {
      display: flex;
      align-items: center;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu {
      position: relative;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-mobile-device {
      display: flex;
      align-items: center;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-mobile-device li {
      display: inline-block;
      position: relative;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-mobile-device a {
      padding: 0 8px;
      color: var(--fontcolor-secondary);
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-mobile-device a i {
      font-size: 25px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-mobile-device .rm-header-booknow-btn {
      background-color: var(--bgcolor-primary);
      color: var(--fontcolor-white);
      padding: 6px 12px;
      border-radius: 4px;
      margin-left: 10px;
      font-size: 15px;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu {
      display: none;
      position: fixed;
      left: 0;
      top: 70px;
      width: 100%;
      background-color: #fff;
      box-shadow: 0 0 30px 0 rgba(51, 77, 136, 0.08);
      border-top: 1px solid var(--linecolor-primary);
      z-index: 9;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul {
      height: auto;
      padding: 15px 0;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li {
      display: inline-block;
      margin: 0;
      width: 100%;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li.rm-desktop-cart-bar,
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li.rm-desktop-search-bar,
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li.rm-desktop-sidebar-menu {
      display: none;
  }
  .rentmy-header .rentmy-menu-header .rentmy-menu-area .rentmy-menu .rentmy-nav-manu ul li a {
      line-height: 37px;
      padding: 0 20px;
      color: #2a2a2a;
  }
  .rentmy-header .rentmy-menu-header .rentmy-rightside-menu {
      display: none;
  }
  .rentmy-header .rentmy-menu-header.shrink {
      height: 70px;
  }
  .rentmy-header .rentmy-menu-header.shrink .rentmy-logo-area .rentmy-logo-area-inner .rentmy-logo-area-middle a img {
      width: unset;
  }
  .rentmy-header .rentmy-menu-header.shrink .rentmy-menu .rentmy-nav-manu ul li a {
      line-height: 37px;
      padding: 0 20px;
  }
}



#dynamic_page_contents {
  margin-top: 120px;
  min-height: 800px;
}
@media (max-width: 991px) {
  #dynamic_page_contents {
    margin-top: 30px;
  } 
}


/*===================== 
product list css  
=======================*/
.RentMyProductItemInner {
  box-shadow: none;
}
.RentMyProductListRow .RentMyFilterArea {
  width: 330px;
  flex: 0 0 auto;
}
.RentMyProductImg {
  padding-top: 0;
  padding-bottom: 0;
}
.RentMyProductImg img {
  height: auto;
  object-fit: cover !important;
}
.RentMyFilterTitle {
    font-size: 20px;
    text-transform: uppercase;
    padding-bottom: 0px;
    height: 50px;
    background-color: #F1EEE6 !important;
    color: #697370;
    line-height: 50px;
    padding-left: 15px;
    font-weight: 700;
}
.RentMyProductBody {
  border-top: none;
}
.ProductName a {
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0;
  color: #171717;
  line-height: 25px;
}
.ProductDetailsIcon, 
.ProductCartIcon {
  display: flex !important;
  cursor: pointer;
}
ul.CategoryMenu {
  margin-top: 10px !important;
}
.CategoryMenu li a {
    padding: 10px 15px;
    color: #555;
    font-size: 15px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    word-spacing: 3px;
    text-decoration: none;
    border-bottom: 1px solid #f2f3f8;
}
.CategoryMenu li a span {
  display: block;
    font-size: 15px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    word-spacing: 3px;
}
.CategoryMenu li a i {
  font-size: 14px;
}
.CategoryMenuList.scrollbar {
  max-height: 320px;
}
.RentMyCheckboxInline .RentMyCheckbox>span, 
.RentMyCheckboxInline .RentMyRadio>span, 
.RentMyCheckbox>span, .RentMyRadio>span {
  top: 2px;
}
.CategoryMenuList.scrollbar {
  max-height: unset;
}
.FilterCheckbox.scrollbar {
  max-height: unset;
  margin-top: 10px;
}
.FilterCheckbox .RentMyCheckbox {
  margin-bottom: 10px !important;
  font-size: 17px;
}
@media (max-width: 767px) {
  .RentMyProductListRow .RentMyFilterArea {
    width: 100%;
  }
  .RentMyFilterArea {
    padding-right: 0;
  }
  .SortProduct {
    width: 100%;
    margin-top: 20px;
  }
  .productlist-search-box {
    width: 100%;
    padding-right: 7px;
  }
  .productlist-search-box .add-item-form-area-new {
    width: 100%;
  }
  .RentMyProductImg img {
    height: 160px !important;
  }
}

/*====================== 
Cart css  
=========================*/
.RentMySummeryTable,
.RentMyCartTable {
  border-left: 1px solid #f2f3f8;
  border-right: 1px solid #f2f3f8;
  border-bottom: 1px solid #f2f3f8;
}
.RentMyCartTable tr th {
  background-color: #f2f3f8;
  border-bottom: none !important;
}
.RentMyCartTable tr td {
  border-color: #f2f3f8;
}
.RentMyTable tr td,
.RentMyTable tr th {
  font-size: 16px;
}
.RentMyCartTotal {
  background-color: #f2f3f8;
}
tbody, 
td, 
tfoot, 
th, 
thead, tr {
  border-top: none !important;
}
.RentMySummeryTable tr td:last-of-type {
  text-align: right;
}
.RentMyCartTotal {
  font-weight: 700;
}
.RentMyTable.RentMySummeryTable tr td, 
.RentMyTable.RentMySummeryTable tr th {
  font-size: 16px;
  padding: .50rem .75rem;
}
.RentMyCartWrapper .RentMyDatePicker input {
  width: 400px !important;
}


/*================== 
checkout css 
====================*/

.CheckoutOrderItem {
  border-top: none;
  border-bottom: 1px solid #eee;
}
.CheckoutOrderList .CheckoutOrderItem:last-child {
  border-bottom: 1px solid #eee;
}
.OrderReviewTitle {
  font-weight: 700;
}
.RentMyWrapper input[type=date], 
.RentMyWrapper input[type=email], 
.RentMyWrapper input[type=number], 
.RentMyWrapper input[type=password], 
.RentMyWrapper input[type=search], 
.RentMyWrapper input[type=tel], 
.RentMyWrapper input[type=text], 
.RentMyWrapper input[type=url], 
.RentMyWrapper select, textarea {
  border: 1px solid #eee;
}
.RentMyWrapper .RentMyInputField {
  border: 1px solid #eee;
}
.FullfillmentPickup {
  border-top: 1px solid #eee;
  padding: 10px 0;
}


/*================================ 
customer portal css  
==================================*/

#RentMyResetPasswordContainer,
#RentMyCustomerRegistrationContainer,
#RentMyCustomerLoginContainer {
  margin-top: 120px;
}
#RentMyResetPasswordContainer,
#RentMyCustomerLoginContainer {
  margin-top: 180px;
}


/*=========================== 
customer portal css  
=============================*/

.RentMyCustomerPortalWrapper {
  padding-top: 100px;
  padding-bottom: 100px;
  margin-top: 0;
  border: none;
}
.RentMyRightContent {
  border-right: 1px solid #e9e9e9;
  border-bottom: 1px solid #e9e9e9;
}
.RentMyPageHeader {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
#RentMyCustomerChangeAvatarContainer .RentMyButtonGroup {
  flex-wrap: wrap;
}
#RentMyCustomerChangeAvatarContainer .RentMyButtonGroup .RentMyUploadBtn {
  margin-bottom: 20px;
}
.RentMyLeftSidebarmenuInner {
  border-right: 1px solid #eee;
}
.RentMyCard {
  border: 1px solid #eee;
  background-color: #fff;
}
.RentMyCardHeader {
  border-bottom: 1px solid #eee;
}
.RentMyCardBody {
  min-height: 375px;
}
.RentMySideMenu ul li a {
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
}
.RentMyPageHeader h3 {
  font-weight: 700;
  font-size: 25px;
}
.RentMyCardHeader h3 {
  font-weight: 700;
}
h5.RentMyProfileName {
  font-weight: 600;
}
.RentMyContentBodyInner {
  border: 1px solid #eee;
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
}

@media (max-width:767px) {
  .RentMyContentBody {
    padding: 20px 0;
  }
  .RentMyCustomerPortalWrapper {
    padding-top: 20px;
  }
  .RentMyProfileImge {
    margin-bottom: 20px;
    padding-top: 20px;
  }
  .RentMyProfileImge img {
    height: 150px;
    object-fit: contain;
  }
}



/*========================= 
footer css 
===========================*/
.rentmy-footer {
  background: #657370;
  flex-shrink: 0;
}
.rentmy-top-footer {
  padding: 50px 0 60px;
}
.rentmy-footer-about h4,
.rentmy-social-links h4,
.rentmy-footer-contact h4,
.rentmy-footer-links h4,
.rentmy-footer-newslatter h4 {
  color: #fff;
  font-size: 17px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  padding-top: 0;
  margin-bottom: 15px;
  text-transform: uppercase;
}
.rentmy-footer-links h4 a {
  color: #fff;
  text-decoration: none;
}
.rentmy-footer-links h4 a:hover {
  color: #dd7842;
}
.rentmy-footer-about {
  text-align: center;
}
.rentmy-footer-about img {
  width: 150px;
  margin-top: 0;
}
.social-icon li a img {
  width: 20px;
  margin-top: -7px;
}
.rentmy-footer-about img.collective-logo {
  width: 40px;
  margin-top: 25px;
}
.rentmy-footer-links img {
  width: 80px;
  margin-top: 10px;
}
.rentmy-footer-about p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 23.8px;
  padding-right: 60px;
}
.social-icon li {
  display: inline-block;
  margin-top: 10px;
  margin-right: 15px;
}
.social-icon li a {
  color: #fff;
}
.social-icon li a i {
  font-size: 25px;
}
.social-icon li a:hover {
  color: #ddd;
}
.rentmy-footer-contact ul {
  padding-left: 0;
}
.rentmy-footer-contact ul li {
  color: #fff;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 8px;
}
.rentmy-footer-contact ul li a {
  color: #fff;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.rentmy-footer-contact ul li i {
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  font-size: 12px;
  margin-right: 8px;
  border-radius: 50px;
  color: #fff;
  background-color: #444;
}
.rentmy-footer-links ul {
  padding-left: 0;
}
.rentmy-footer-links ul li {
  padding-bottom: 5px;
  font-size: 15px;
  display: inline-block;
  margin-right: 22px;
padding-top: 5px;
    padding-bottom: 18px;
}
.rentmy-footer-links ul li a {
  color: #eee;
  font-size: 17px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: none;
}
.rentmy-footer-links ul li a:hover {
  color: #dd7842;
}
.rentmy-footer-links ul li a i {
  margin-right: 4px;
}
.rentmy-bottom-footer {
  background-color: #fff;
  padding-top: 15px;
  padding-bottom: 15px;
  text-align: center;
}
.rentmy-bottom-footer-content {
  flex: 0 0 100%;
  max-width: 100%;
  margin-top: 15px;
}
.rentmy-bottom-footer-content p {
  color: #777;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.rentmy-bottom-footer-content p a {
  color: #777;
  font-weight: 400;
}
.rentmy-bottom-footer-content p a:hover {
  text-decoration: underline;
}
.rentmy-bottom-top-footer ul  {
  display: flex;
  justify-content: space-between;
}
.rentmy-bottom-top-footer ul li {
  display: inline-block;
  position: relative;
  width: 33.333333%;
}
.rentmy-bottom-top-footer ul li::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  background-color: #eee;
  width: 1px;
  height: 20px;
}
.rentmy-bottom-top-footer ul li a {
  color: #2f394e;
      font-size: 12px;
  font-weight: bold;
font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
text-transform: uppercase;
text-decoration: none;
}
.rentmy-bottom-top-footer {
width: 100%;
}
@media (max-width: 991px) {
  .rentmy-footer-about {
    text-align: center;
    margin-bottom: 30px;
  }
  .rentmy-top-footer {
    padding:40px 0 50px;
  }
}
@media (max-width: 767px) {
  .rentmy-bottom-top-footer ul {
    flex-wrap: wrap;
  }
  .rentmy-bottom-top-footer ul li {
    width: 100%;
  }
  .rentmy-footer-about h4,
  .rentmy-social-links h4,
  .rentmy-footer-contact h4,
  .rentmy-footer-links h4,
  .rentmy-footer-newslatter h4 {
    font-size: 15px;
  }
}
@media (max-width: 575px) {
  .rentmy-top-footer {
    padding: 50px 0 50px;
  }
  .rentmy-footer-links {
    text-align: center;
  }
  .rentmy-bottom-footer-content p {
    padding-left: 10px;
    padding-right: 10px;
  }
  .social-icon li a i {
    font-size: 20px;
  }
}




/*============================== 
Innerpage banner  
================================*/
.rentmy-innerpage-banner {
  margin-top: 0px;
  background-image: url("../image/VGC-Page-Title-Banner.webp");
  height: 235px;
  background-position: center;
}
.rentmy-innerpage-overlay {
  height: 100%;
}
.rentmy-innerpage-overlay .container {
  height: 100%;
}
.container-inner {
  height: 100%;
}
.container-inner-overlay {
  height: 100%;
}
.rentmy-innerpage-banner .row {
  height: 100%;
}
.rentmy-innerpage-banner .rentmy-innerpage-body {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  height: 100%;
}
.rentmy-innerpage-body-inner {
  width: 100%;
  text-align: center;
}
.rentmy-innerpage-banner h1 {
  font-size: 4em;
  font-weight: 600;
  padding-bottom: 5px;
  color: #fff;
}
.rentmy-breadcrumbs {
    display: flex;
    align-items: center;
    justify-content: center;
}
.rentmy-innerpage-banner ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}
.rentmy-innerpage-banner ul li {
  position: relative;
  float: left;
  padding: 0 15px;
  color: #fff;
  font-weight: 500;
}
.rentmy-innerpage-banner ul li:before {
  content: "\00bb";
  position: absolute;
  top: 0px;
  left: -6px;
}
.rentmy-innerpage-banner ul li:first-child::before {
  display: none;
}
.rentmy-innerpage-banner ul li a {
  color: #fff;
  font-weight: 500;
  text-decoration: none;
}
.rentmy-innerpage-banner ul li:first-child {
  padding-left: 0;
}
.overlay h3 a::after {
  display: none;
}


@media (max-width: 992px) {
  .rentmy-innerpage-banner {
      height: 90px;
  }
  .rentmy-innerpage-banner h1 {
    font-size: 30px;
    margin-bottom: 0;
    line-height: 30px;
    padding-bottom: 0;
  }
  .rentmy-innerpage-banner ul li,
  .rentmy-innerpage-banner ul li a {
    font-size: 14px;
  }
}



/*============================ 
how rental works css   
==============================*/

.content-top-text {
  padding-top: 50px;
  padding-bottom: 50px;
}
.paragraph-content {
  align-items: center;
  margin-bottom: 50px;
}
.paragraph-icon {
  width: 35px;
  margin-bottom: 12px;
}
.paragraph-content p {
  padding-bottom: 10px;
}
.paragraph-content-body-left {
  padding-right: 100px;
}
.paragraph-content-body-right {
  padding-left: 100px;
}
.fl-rich-text {
   margin-bottom: 40px;
}
.fl-rich-text a {
  color: #5e6c68f2;
    text-decoration: none;
    font-size: 26px;
    font-family: "Crimson Text", serif;
}
.showroom-location .container-inner {
  background-color: #f0ece1;
  padding-top: 80px;
  padding-bottom: 80px;
  padding-left: 15px;
  padding-right: 15px;
}
.showroom-img {
  text-align: center;
}
.showroom-img img {
  width: 170px;
  object-fit: contain;
}
.showroom-content .fl-rich-text a {
  font-size: 17px;
  font-family: 'Raleway', sans-serif;
}
.event-planing-section {
  background-color: #f0ece1;
  margin-top: 80px;
  padding-top: 50px;
  padding-bottom: 40px;
}
.event-planing-section .row {
  align-items: center;
}
.event-planing-content {
  position: relative;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 80px;
  padding-right: 100px;
  padding-bottom: 80px;
  padding-left: 100px;
  z-index: 2;
  margin-top: 5%;
}
.event-planing-content h2 {
  margin-bottom: 35px;
  line-height: 43px;
}
.event-planing-content h2 span {
  font-size: 60px;
    line-height: 40px;
    font-weight: 400;
}
.event-planing-img {
  position: relative;
  z-index: 1;
  margin-bottom: -10%;
    margin-left: -20%;
}

@media (max-width: 767px) {
  .event-planing-section {
    margin-top: 30px;
  }
  .order-2 {
    order: -2 !important;
  }
  .paragraph-content-body {
    padding-top: 20px;
    text-align: center;
  }
  .paragraph-content-body-left {
    padding-right: 0px;
  }
  .paragraph-content-body-right {
    padding-left: 0px;
  }
  .showroom-img img {
    width: 120px;
    margin-bottom: 30px;
  }
  .event-planing-content {
    padding: 20px;
    text-align: center;
    margin-top: 0;
  }
  .event-planing-content h2 span {
    font-size: 30px;
  }
  .showroom-location .container-inner {
    padding-top: 30px;
    padding-bottom: 30px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .content-top-text {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}


/*============================= 
FAQ css  
===============================*/

.accordion-list {
  margin-top: 40px;
}
.accordion-list h2 {
  font-size: 32px;
  margin-bottom: 40px;
}
.accordion-list .card {
  border: none;
}
.accordion-list .card-header {
  padding: 5px 15px 5px 20px;
  margin-bottom: 0;
  background: #f0ece1;
  border-bottom: none;
  margin-bottom: 12px;
}
.accordion-list .btn {
  background: #f0ece1;
  width: 100%;
  text-align: left;
  font-size: 24px;
  color: #343e4c;
  font-weight: 600;
  text-decoration: none !important;
  gap: 10px;
  font-family: 'Crimson Text', serif;
  border: none;
  outline: 0;
}
.accordion-list .btn:hover,
.accordion-list .btn:focus,
.accordion-list .btn:active {
  box-shadow: none;
}
.accordion-list .btn i {
  font-size: 17px;
    margin-right: 20px;
}
.btn.btn-link.btn-block.text-left .fa-plus {
  display: none;
}
.btn.btn-link.btn-block.text-left .fa-minus {
  display: inline-block;
}
.btn.btn-link.btn-block.text-left.collapsed .fa-plus {
  display: inline-block;
}
.btn.btn-link.btn-block.text-left.collapsed .fa-minus {
  display: none;
}


/*==================== 
reviews css  
======================*/
.reviews-section .container {
  padding-bottom: 80px;
  border-bottom: 1px solid #cccccc;
}
.reviews-section.localdecorclients-section .container {
  padding-bottom: 0px;
  border-bottom: none;
}
.fl-icon-group {
  display: inline-flex;
  margin-left: 40px;
}
.fl-icon-group i {
  color: #ae6a45;
  font-size: 30px;
}
.reviews-experience-title {
  margin-top: 20px;
}
.review-section-title {
  font-size: 32px;
  margin-bottom: 80px;
  margin-top: 50px;
}
.review-box {
  background: #e7e7e7;
  padding: 30px;
  position: relative;
  text-align: center;
}
.localdecorclients-section .review-box {
  background: #f0ece1;
}
.review-box img {
  width: 55px !important;
  margin-top: -80px;
  display:  unset !important;
}
.reviews-section .owl-stage-outer.owl-height {
  height: auto !important;
  padding-top: 30px;
}
.reviews-section .owl-nav {
  display: none;
}
.reviews-section.localdecorclients-section .owl-nav {
  display: block;
}
.reviews-section.localdecorclients-section .owl-carousel .owl-nav button.owl-next, 
.reviews-section.localdecorclients-section .owl-carousel .owl-nav button.owl-prev {
  background-color: #5e6c68f2;
  width: 28px;
  height: 28px;
  border-radius: 0;
  margin: 0;
  color: #fff;
}
.reviews-section.localdecorclients-section .owl-carousel .owl-nav button.owl-next:hover, 
.reviews-section.localdecorclients-section .owl-carousel .owl-nav button.owl-prev:hover {
  background-color: #9c5a36;
}
.reviews-section.localdecorclients-section .owl-theme .owl-nav .disabled {
  opacity: unset;
}
.reviews-section.localdecorclients-section .owl-theme .owl-nav {
  margin-top: 5px;
  position: absolute;
  bottom: -35px;
  left: 0;
  right: 0;
}
@media (max-width: 767px) {
  .reviews-section .container {
    padding-bottom: 0px;
  }
  .review-box {
    margin-bottom: 50px;
  }
}


/*======================== 
Blog css  
==========================*/
.blog-main-title {
  position: relative;
}
.blog-main-title::after {
  content: "";
    position: absolute;
    top: 45px;
    left: -190px;
    background-color: #343e4c;
    width: 175px;
    height: 5px;
}
.blog-list-content input[type="radio"]{
  display: none;
}
.blog-list-content .tabs label{
  display: inline-block;
  border-style: none;
    border-width: 0;
    background-clip: border-box;
    border-color: rgba(94, 108, 104, 0.95);
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    padding-top: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
    cursor: pointer;
}
.blog-list-content .tabs .tab-content-blog {
  margin-top: 40px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}
.tab-content-blog .row {
  margin-left: -20px;
  margin-right: -20px;
}
.tab-content-blog .col-lg-6 {
  padding-left: 20px;
  padding-right: 20px;
}
.blog-list-content .tabs .content {
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}
.blog-list-content .tabs .content img {
  height: 330px;
  width: 100%;
  object-fit: cover;
  transition: all .3s;
}
.blog-list-content .tabs .content:hover img {
  transform: scale(1.1);
}
.blog-list-content .tabs label:not(:first-of-type){
  margin-left: -4px;
}
.blog-list-content input[type="radio"]:checked + label{
  background-color: transparent;
  cursor: default;
}
.content a {
  position: relative;
  display: block;
}
.content a::after {
  content: "";
    display: block;
    height: 100%;
    width: 100%;
    position: absolute;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, .7) 100%);
    -webkit-transition: background-color .3s ease;
    -moz-transition: background-color .3s ease;
    -o-transition: background-color .3s ease;
    transition: background-color .3s ease;
}
.blog-list-content .tabs .overlay{
  position: absolute;
  width: 100%;
  bottom:0;
  color: #fff;
  left: 0;
  transition: all .5s;
  transition: all .5s linear;
text-align: left;
padding-left: 25px;
}
.blog-list-content h3 {
  color: #fff;
}
.blog-list-content h3 a {
  color: #fff;
  font-size: 26px;
  text-decoration: none;
}
.blog-list-content p {
  color: #fff;
  font-size: 12px;
}
@keyframes animateTabe{
  0%{
    opacity: 0;
  }
  100%{
    opacity: 1;
  }
}

.classic-list{
  list-style-type: none;
}
@media (max-width:767px) {
  .blog-list-content .tabs label {
    padding-top: 5px;
    padding-right: 15px;
    padding-bottom: 5px;
    padding-left: 15px;
  }
  .blog-list-content .tabs .content {
    margin-bottom: 15px;
  }
  .blog-list-content h3 a {
    font-size: 20px;
  }
}



/*========================== 
consultation css  
=============================*/

.consultation-content {
  padding-top: 60px;
}
.consultation-content .paragraph-img {
  text-align: right;
}
.consultation-content .paragraph-img img {
  width: 365px;
}
.consultation-content p {
  padding-right: 25%;
}
.vgc-cookies iframe {
  height: 647px;
}
._3efP_GeH5kyBAzqnLzL.w_Mtb2W9166EgOSv9i3M {
  width: 95%;
  border: 1px solid var(--text-color-level3, rgba(26, 26, 26, 0.1));
  border-radius: 8px;
  box-shadow: 0 1px 8px 0 rgb(0 0 0 / 8%);
}
._3efP_GeH5kyBAzqnLzL {
  position: relative;
  width: 100%;
  background-color: var(--color-bg-white-l-1, rgba(255, 255, 255, 1));
}
.consult-text {
  font-size: 14px;
  text-align: center;
}
.consult-text a {
  font-size: 14px;
  font-family: "Raleway", sans-serif;
}
@media (max-width: 767px) {
  .consultation-content p {
    padding-right: 0;
  }
}


/*========================= 
about us css  
===========================*/
.about-experiences {
  padding-top: 70px;
}
.team-box {
  display: flex;
  align-items: center;
  gap: 45px;
  margin-bottom: 60px;
}
.team-box img {
  width: 315px;
}
.team-box h3 {
  color: #ae6a45;
  margin-bottom: 15px;
}
.team-box span {
  font-family: 'Crimson Text', serif;
  font-size: 20px;
  color: #343e4c;
  font-weight: 600;
}
.aboutstory-section .container {
  background-color: #f0ece1;
  padding: 70px 40px;
}
.aboutstory-section h3 {
  font-size: 32px;
}
.aboutstory-section .paragraph-content-body img {
  float: right;
  margin-bottom: -156px;
  width: 260px;
}
blockquote {
  border-left: 5px solid #dddddd;
  margin: 1.5em 1em 1.5em 3em;
  font-size: 1.1em;
  line-height: inherit;
  position: relative;
  padding: 1.2em;
  font-style: italic;
}
blockquote p {
  margin-bottom: 1.6em;
  font-size: 16px;
  line-height: 30px;
  padding-right: 38%;
}
blockquote p span {
  font-size: 16px;
  color: #000;
}
.about-ourlocation {
  padding-top: 80px;
}
.about-ourlocation h3 {
  margin-bottom: 30px;
}
.about-ourlocation p {
  margin-bottom: 30px;
}
.about-ourlocation iframe {
  width: 100%;
  height: 400px;
}
.about-member {
  padding-top: 40px;
  padding-bottom: 60px;
  text-align: center;
}
.about-member h2 {
  margin-bottom: 30px;
}
.about-member img {
  width: 150px;
}
.contact-information ul {
  display: flex;
  list-style: none;
}
.contact-information ul li {
  width: 50%;
}
.contact-information ul li a {
  display: block;
  text-align: center;
  padding-left: 40px;
  padding-right: 40px;
}
.contact-information ul li a img {
  width: 45px;
}
@media (max-width: 767px) {
  .contact-information ul {
    flex-wrap: wrap;
    padding: 0;
    list-style: none;
  }
  .contact-information ul li {
    width: 100%;
    margin-bottom: 30px;
  }
  .contact-ordinary {
    padding-top: 30px;
    padding-bottom: 0px;
  }
  .about-experiences {
    padding-top: 30px;
  }
  .start-planing-content {
    flex-wrap: wrap;
    text-align: center;
    justify-content: center !important;
  }
  .start-planing-rightside {
    text-align: center;
  }
  .about-experiences .vivian-btn {
    margin-bottom: 15px;
  }
}


/*========================= 
contact css  
===========================*/
.contact-ordinary {
  padding-top: 60px;
  padding-bottom: 80px;
}
.ordinary-img {
  width: 45px;
}
.contact-information {
  padding-top: 80px;
}
.contact-information img {
  margin-bottom: 15px;
}
.contact-information h4 {
  font-size: 18px;
  color: #808285;
}
.start-planing {
  background-color: #f0ece1;
  padding-top: 80px;
  padding-bottom: 80px;
}
.start-planing-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.start-planing-content h2 {
  margin-bottom: 20px;
}


/*============================= 
decor gallery css  
===============================*/

.wedding-gallrey-top {
  padding-top: 70px;
}
.wedding-gallery-section {
  margin-bottom: 100px;
}
.wedding-gallery-list {
  column-count: 4;
  --webkit-column-count: 4;
  --moz-column-count: 4;
  gap: 30px;
}
.wedding-gallery-item {
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}
.wedding-gallery-item img {
  transition: all 0.3s;
  transform-style: preserve-3d;
  will-change: transform;
}
.wedding-gallery-item:hover img {
  transform: scale(1.1);
}
.wedding-gallery-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, .5);
  width: 100%;
  height: 100%;
  display: none;
}
.wedding-gallery-item:hover::after {
  display: block;
}
@media (max-width:767px) {
  .wedding-gallery-list {
    column-count: 2;
    gap: 10px;
  }
  .wedding-gallery-item {
    margin-bottom: 10px;
  }
  .wedding-gallrey-top {
    padding-top: 30px;
  }
  .wedding-gallrey-top .paragraph-content {
    margin-bottom: 0px;
  }
}




/*=========================== 
wedding rental css  
============================*/

.event-planing-bgwhite {
  background-color: #fff;
  padding-top: 0;
}
.event-planing-bgwhite .event-planing-content {
  background-color: #f0ece1;
  padding-left: 70px;
}
.event-planing-bgwhite .event-planing-img {
  margin-left: -48%;
}
.event-planing-bgwhite .uabb-second-heading-text {
  color: #ae6a45;
}
.event-planing-bgwhite h3 {
  margin-bottom: 40px;
}
.event-planing-bgwhite p {
  font-size: 18px;
}
.rental-choose-section {
  padding-top: 100px;
}
.rental-choose-section h2 {
  margin-bottom: 40px;
}
.rental-choose-list {
  display: flex;
  flex-wrap: wrap;
}
.rental-choose-item {
  width: 50%;
  margin-bottom: 50px;
  text-align: center;
}
.rental-choose-item h4 {
  color: #ae6a45;
  font-size: 70px;
  font-weight: 800;
  letter-spacing: 2px;
  margin-bottom: 20px;
}
.rental-choose-item span {
  font-size: 1.5rem;
    letter-spacing: 1.5px;
    color: rgba(94, 108, 104, 0.95);
    font-weight: 600;
    font-family: 'Crimson Text', serif;
}

.shopby-category h2 {
  margin-bottom: 50px;
}
.shop-category-list {
  display: flex;
  flex-wrap: wrap;
  margin-left: -20px;
  margin-right: -20px;
}
.shop-category-item {
  display: block;
  width: 20%;
  padding-left: 20px;
  padding-right: 20px;
  margin-bottom: 50px;
}
.collection-shop-list .shop-category-item {
  width: 33.333333%;
}
.shop-category-item img {
  width: 100%;
  height: 244px;
  object-fit: cover;
}
.collection-shop-list .shop-category-item img {
  height: 440px;
}
.category-body {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0ece1;
  height: 70px;
  padding-left: 15px;
  padding-right: 15px;
}

.category-body h4 {
  color: #ae6a45;
  font-size: 1.41176470588rem;
    line-height: 1.2em;
    margin-bottom: 0;
}
.shop-category-item:hover .category-body h4 {
  color: #526b6b;
}
@media (max-width: 767px) {
  .event-planing-bgwhite .event-planing-img {
    margin-left: 0;
  }
  .event-planing-bgwhite .event-planing-content {
    padding-left: 20px;
  }
  .event-planing-img {
    margin-bottom: 0;
    margin-left: 0;
  }
  .event-planing-bgwhite p {
    font-size: 16px;
  }
  .rental-choose-section {
    padding-top: 0px;
  }
  .shop-category-list {
    margin-left: -7.5px;
    margin-right: -7.5px;
  }
  .shop-category-item {
    width: 50%;
    padding-left: 7.5px;
    padding-right: 7.5px;
    margin-bottom: 15px !important;
  }
  .collection-shop-list .shop-category-item {
    width: 50%;
  }
  .shop-category-item img {
    height: 130px;
  }
  .category-body h4 {
    font-size: 18px;
    text-align: center;
  }
}


/*=================================== 
Decorating services css  
===================================*/

.overwhelming-box {
  float: right;
  background-color: #ffffff;
  width: 480px;
    border-radius: 10px;
    box-shadow: 1px 1px 18px 0px rgba(0, 0, 0, 0.11);
    padding-top: 70px;
    padding-right: 60px;
    padding-bottom: 60px;
    padding-left: 60px;
    text-align: center;
}
.overwhelming-box img {
  width: 78px;
  margin-top: -175px;
}
.fl-callout-title {
  margin-bottom: 30px;
}
.fl-callout-title a {
  font-family: "Crimson Text", serif;
}
.rental-steps-section {
  padding-bottom: 70px;
}
.rental-steps-section .content-top-text p {
  font-style: italic;
}
.rental-steps-box {
  background-color: #5e6c68f2;
  min-height: 348px;
  padding: 30px;
  border-radius: 10px;
}
.rental-steps-box h3 {
  font-size: 28px;
  color: #fff;
  text-align: center;
}
.rental-steps-box p {
  color: #fff;
}
.rental-steps-box ul {
  list-style: disc;
}
.rental-steps-box ul li {
  font-size: 16px;
  color: #fff;
  padding-bottom: 3px;
}
.viewfullgallery {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #9c5a36;
  font-weight: 600;
}
.viewfullgallery i {
  font-size: 25px;
  margin-left: 15px;
}
.needs-idea-section .showroom-content {
  padding-left: 25px;
}
.needs-idea-section .row {
  align-items: center;
}
.idea-img-box {
  overflow: hidden;
}
.idea-img-box img {
  transition: all 0.3s;
}
.idea-img-box:hover img {
  transform: scale(1.1);
}
.diydecor-section.consultation-content .paragraph-img {
  text-align: left;
}
.diydecor-section.consultation-content .paragraph-content-body-right {
  padding-left: 15px;
}
.diydecor-section.consultation-content p {
  padding-right: 0;
}
@media (max-width: 767px) {
  .rental-steps-section {
    padding-bottom: 30px;
  }
  .rental-steps-box {
    margin-bottom: 15px;
  }
  .overwhelming-box {
    float: unset;
    width: 100%;
    padding: 30px 20px;
  }
  .overwhelming-box img {
    width: 45px;
    margin-top: -76px;
  }
  .needs-idea-section .showroom-content {
    padding-left: 0px;
    margin-bottom: 20px;
  }
  .needs-idea-section .showroom-content .fl-rich-text {
    margin-bottom: 15px;
  }
  .idea-img-box {
    margin-bottom: 15px;
  }
  .diydecor-section.consultation-content .paragraph-content-body-right {
    padding-left: 0px;
  }
}


/*========================== 
floral rental css  
============================*/

.silkflower-img {
  width: 500px;
}



/*========================== 
Local Weddings css  
============================*/

.decor-service-rental .shop-category-list .shop-category-item {
  width: 100%;
}
.decor-service-rental .col-md-6 {
  padding-left: 50px;
  padding-right: 50px;
}

.design-consultation .container-inner {
  background-color: #5e6c68f2;

}
.design-consultation .showroom-content {
  padding-left: 40px;
}
.design-consultation .showroom-content h3,
.design-consultation .showroom-content p {
  color: #fff;
}
.add-item-form-area-new {
  width: 320px;
}
.add-item-form-area-new input {
  outline: 0;
  box-shadow: none;
  box-shadow: none !important;
}
.RentMyWrapperProductList {
  margin-top: 40px;
}
.FilterCheckbox a {
  font-size: 15px;
  color: #333 !important;
}
.SortProductRow {
  margin-bottom: 30px;
}
.RentMyProductImg img {
  margin-left: auto;
    margin-right: auto;
    display: block;
    z-index: -4;
    object-fit: contain;
    height: 320px;
    width: 100%;
}
.ProductName a {
  font-weight: 600;
    font-size: 20px;
    letter-spacing: 1.5px;
    transition: all .5s;
    padding-top: 10px !important;
    color: #697370;
}
h5.ProductPrice,
.ProductPrice {
  font-size: 16px !important;
  font-weight: 600 !important;
  /* font-family: "Raleway", sans-serif; */
  color: #333;
  margin-bottom: 0;
}
/* .RentMyProductOverlay {
  position: absolute;
  top: unset !important;
  background-color: #697370;
  width: 100%;
  height: 45px;
  bottom: 0;
  justify-content: space-between;
} */
.RentMyProductOverlay {
  background-color: transparent;
}
.ProductDetailsIcon, 
.ProductCartIcon {
  background-color: #5e6c68f2;
}
/* .ProductDetailsIcon i, 
.ProductCartIcon i {
  font-size: 22px;
} */
.RentMyWrapper .RentMyInputField {
  font-size: 15px;
}
.RentMyMiniCart .MiniContinueShoppingBtn,
.RentMyMiniCart .MiniCartBtn {
  color: #fff !important;
  display: flex !important;
}
.RentMyWrapper .RentMyBtn {
  background-color: #697370 !important;
}
.RentMyWrapper .RentMyBtn:hover {
  background-color: #a36136 !important;
}
@media (max-width: 767px) {
  .decor-service-rental .col-md-6 {
    padding-left: 15px;
    padding-right:15px;
  }
  .design-consultation .showroom-content {
    padding-left: 0px;
    text-align: center;
  }
  .fl-module .vivian-btn:nth-of-type(2),
  .btn-area .vivian-btn:nth-of-type(2) {
    margin-top: 15px;
  }
  .fl-rich-text {
    margin-bottom: 20px;
  }
  .fl-icon {
    margin-right: 10px;
  }
  .fl-icon-group .fl-icon i {
    font-size: 18px;
  }
  .ProductName a {
      font-size: 15px;
    line-height: 20px;
  }
}

.terms-conditions-content {
  padding-top: 50px;
}

.form-group label,
.form-label {
  font-family: "Crimson Text", serif;
  color: #343434 !important;
  font-weight: 600;
  margin-bottom: 4px;
}
.form-group {
  margin-bottom: 15px;
}
.form-label strong{
  color: #790000;
    margin-left: 4px;
    font-weight: 600;
    font-size: 15px;
}
.form-group small {
  font-family: "Crimson Text", serif;
    font-weight: 400;
    font-size: 13px;
    color: #343434;
}
.form-control {
  height: 50px;
  border: 1px solid#333333;
  border-radius: 0;
  box-shadow: none;
  outline: 0;
}
.form-control:focus,
.form-control:active,
.form-control:hover {
  border: 1px solid#333333;
  box-shadow: none;
  outline: 0;
}
.SortProduct .RentMyInputField {
  height: 50px;
  border: 1px solid #ddd;
}
.add-item-form-area-new input {
  border: 1px solid #ddd !important;
  border-radius: 3px;
}
.home-search-box {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}
.home-search-box-inner {
  border: 1px solid #fff;
  border-radius: 10px;
  padding: 10px;
}
.home-search-box-inner .add-item-form-area-new {
  width: 450px;
}
.home-search-box-inner .add-item-form-area-new input {
  border: none !important;
  border-radius: 10px;
}
.vgc-top-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #9c5a36;
  height: 42px;
  padding-left: 15px;
  padding-right: 15px;
}
.vgc-top-bar span {
  font-size: 15px;
  color: #fff;
  font-weight: 400;
}
.innerpage-text-left {
  text-align: left !important;
}
.RentMyCheckbox, 
.RentMyRadio{
    display: inline-block;
    position: relative;
    padding-left: 25px;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
    -webkit-transition: all .3s;
    transition: all .3s;
    padding-right: 10px;
    color: #242424;
    font-weight: 500;
}
textarea.form-control {
  height: 150px;
}
._3efP_GeH5kyBAzqnLzL {
  background-color: transparent;
}
.small-font {
  font-size: 12px;
  font-weight: 600;
}
@media (max-width: 1450px) {
  .gallery-inspiration-event .vivian-btn {
    padding: 12px 20px;
    margin-right: 12px;
    font-size: 15px;
  }
  .gallery-inspiration-event .event-planing-content{
    padding-right: 30px;
    padding-left: 30px;
  }
}
@media (max-width: 767px) {
  .home-search-box-inner .add-item-form-area-new {
    width: 300px;
  }
}



/*============================= 
partner login css  
===============================*/

#RentMyCustomerLoginContainer.PartnerLogin .LoginElement,
#RentMyCustomerRegistrationContainer.PartnerRegister .RegistrationElement {
  display: flex;
  align-items: center;
  width: 900px;
  min-height: 500px;
  box-shadow: 0 0 10px #ddd;
  padding: 0;
  border: none;
}
#RentMyCustomerLoginContainer.PartnerLogin form {
  padding-left: 25px;
  padding-right: 25px;
}
.LoginElementLeftSide {
  width: 45%;
  padding: 25px 0 25px 25px;
}
.LoginElementRightSide {
  width: 55%;
  position: relative;
  padding-left: 30px;
}
#RentMyCustomerLoginContainer .NewAccount,
#RentMyCustomerLoginContainer .ForgotPassword {
  color: #555;
  font-weight: 500;
  font-size: 12px;
}
.AuthSubTitle {
  font-size: 13px;
  color: #666;
  display: block;
  font-weight: 400;
}
#RentMyCustomerRegistrationContainer .RentMyInputField, 
#RentMyCustomerLoginContainer .RentMyInputField {
  background-color: transparent;
}
#RentMyCustomerRegistrationContainer.PartnerRegister .RegistrationElement select {
  margin-top: 0;
}

@media (max-width: 767px) {
  .LoginElementLeftSide {
    width: 100%;
    padding: 20px;
  }
  .LoginElementRightSide {
    display: none;
  }
  #RentMyCustomerLoginContainer.PartnerLogin form {
    padding-left: 0;
    padding-right: 0;
  }
  .RentMyWrapper input[type=date], 
  .RentMyWrapper input[type=email], 
  .RentMyWrapper input[type=number], 
  .RentMyWrapper input[type=password], 
  .RentMyWrapper input[type=search], 
  .RentMyWrapper input[type=tel], 
  .RentMyWrapper input[type=text], 
  .RentMyWrapper input[type=url], 
  .RentMyWrapper select, textarea {
    border: 1px solid #ccc;
  }
  .RentMyMiniCart .RentMyCartBody {
    right: unset;
    left: 20px;
  }
  .RentMyMiniCart .RentMyTopArrow {
    top: -15px;
    right: unset;
    left: 23px;
  }
}


/*============================== 
scroll top button css  
===============================*/


#scroll-top-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(94, 108, 104, 0.95);
  font-size: 15px;
  width:30px;
  height: 30px;
  text-align: center;
  border-radius: 4px;
  position: fixed;
  bottom: 30px;
  right: 30px;
  transition: background-color .3s, 
    opacity .5s, visibility .5s;
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
  color: #fff;
}
#scroll-top-button:hover {
  cursor: pointer;
  background-color: rgba(94, 108, 104, 0.95);
}
#scroll-top-button:active {
  background-color: rgba(94, 108, 104, 0.95);
}
#scroll-top-button.show {
  opacity: 1;
  visibility: visible;
}


/*=================== 
Blog css  
=====================*/
.blog-details-section {
  padding-top: 100px;
  padding-bottom: 80px;
}
.fl-row:before, 
.fl-row:after, 
.fl-row-content:before, 
.fl-row-content:after, 
.fl-col-group:before, 
.fl-col-group:after, 
.fl-col:before, 
.fl-col:after, 
.fl-module:before, 
.fl-module:after, 
.fl-module-content:before, 
.fl-module-content:after {
  display: table;
  content: " ";
}
.fl-col {
  float: left;
  min-height: 1px;
}
.blog-details-section img {
  width: 100%;
  max-width: 100%;
  height: auto;
  padding-left: 30px;
  padding-right: 30px;
  margin-top: 30px;
  margin-bottom: 30px;
}
.blog-details-section strong {
  font-weight: bold;
  color: #343e4c;
}
.blog-details-section h1, 
.blog-details-section h2, 
.blog-details-section h3, 
.blog-details-section h4, 
.blog-details-section h5, 
.blog-details-section h6 {
  margin-bottom: 20px;
  padding-left: 30px;
  padding-right: 30px;
}
.blog-details-section h1 strong, 
.blog-details-section h2 strong, 
.blog-details-section h3 strong, 
.blog-details-section h4 strong, 
.blog-details-section h5 strong, 
.blog-details-section h6 strong {
  font-family: 'Crimson Text', serif;
  font-weight: 600;
}
.blog-details-section h2 {
  color: #343e4c;
  font-size: 1.88235294118rem;
  line-height: 1.3em;
}
.blog-details-section h3 {
  font-size: 1.52941176471rem;
  font-family: 'Crimson Text', serif;
  line-height: 1.3em;
}
.blog-details-section ol li,
.blog-details-section ul li {
  line-height: 28px;
}

.blog-details-section p, 
.blog-details-section p {
  margin-bottom: 1.6em;
  padding-left: 30px;
  padding-right: 30px;
}
.fl-photo-caption {
  padding-left: 30px;
  padding-right: 30px;
}
.blog-details-section a, 
.blog-details-section .page-title {
  color: #5e6c68f2;
  font-size: 15px;
}
.blog-details-section iframe {
  height: 618px;
}
.fl-node-x7ac1num680i {
  width: 50%;
}
.fl-node-x0ecpksrfun6 {
  width: 50%;
}
.fl-node-456olshj3ywb,
.fl-node-awuoxsdfr9bm,
.fl-node-djmcu5z7xfo4,
.fl-node-yb4kz1esw2uf,
.fl-node-09gej2tbnlfv,
.fl-node-3s8lk4ch2u07,
.fl-node-dgwcmzbu56e9,
.fl-node-wxeh0kbsy5do,
.fl-node-57e6idkhx4ag,
.fl-node-4gkqhmjon1cl,
.fl-node-gx1sdpfhemkb,
.fl-node-yw1prgfzt8j2,
.fl-node-e4g59rypcw7m,
.fl-node-zpguy51i2wjf,
.fl-node-7a4iuy159zkv,
.fl-node-u6f7lk0rj42y,
.fl-node-h7l4jbi5g9un,
.fl-node-d1kmajlerfh7,
.fl-node-dcwuynaijgrk,
.fl-node-y7saodx20gm4 {
  width: 50%;
}
.wedding-reception img {
  width: 80%;
  max-width: 80%;
}
.fl-node-dtv79ko45mge,
.fl-node-wye9lhx145u0,
.fl-node-1r76iqnvhobw,
.fl-node-lowjxyhpsair,
.fl-node-aj72t1h36vpy {
  width: 20%;
  padding-left: 30px;
  padding-right: 30px;
}
.fl-node-dtv79ko45mge img,
.fl-node-wye9lhx145u0 img,
.fl-node-1r76iqnvhobw img,
.fl-node-lowjxyhpsair img,
.fl-node-aj72t1h36vpy img {
  padding-left: 0px;
  padding-right: 0px;
}
.fl-col-group-equal-height{
    display: flex;
    flex-wrap: wrap;
    width: 100%;
}
.RentMySummeryTable tr td span, 
.RentMySummeryTable tr td h5 {
  font-family: "Raleway", sans-serif;
}
.RentMyCardBody {
  min-height: 271px;
}
.RentMyContentBodyInner {
  border: none;
  padding: 0;
}
.fl-node-gwnblfuoik2a {
  width: 33%;
}
.fl-node-82aoyx0zs53b {
  width: 67%;
}
.fl-node-6kyu1pni7x9q {
  width: 37%;
}
.fl-node-bki8urjl4376 {
  width: 63%;
}
@media (max-width: 767px) {
  .fl-node-x7ac1num680i {
    width: 100%;
  }
  .fl-node-x0ecpksrfun6 {
    width: 100%;
  }
  .fl-node-djmcu5z7xfo4,
.fl-node-yb4kz1esw2uf,
  .fl-node-09gej2tbnlfv,
.fl-node-3s8lk4ch2u07,
.fl-node-dgwcmzbu56e9,
.fl-node-wxeh0kbsy5do,
.fl-node-57e6idkhx4ag,
.fl-node-4gkqhmjon1cl,
.fl-node-gx1sdpfhemkb,
.fl-node-yw1prgfzt8j2,
.fl-node-e4g59rypcw7m,
.fl-node-zpguy51i2wjf,
.fl-node-7a4iuy159zkv,
.fl-node-u6f7lk0rj42y,
.fl-node-h7l4jbi5g9un,
.fl-node-d1kmajlerfh7,
.fl-node-dcwuynaijgrk,
.fl-node-y7saodx20gm4 {
    width: 100%;
  }
  .wedding-reception img {
    width: 100%;
    max-width: 100%;
  }
  .fl-node-dtv79ko45mge,
  .fl-node-wye9lhx145u0,
  .fl-node-1r76iqnvhobw,
  .fl-node-lowjxyhpsair,
  .fl-node-aj72t1h36vpy {
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
  }
  .fl-node-gwnblfuoik2a {
    width: 100%;
  }
  .fl-node-82aoyx0zs53b {
    width: 100%;
  }
}
  
[selector="paginattion_area"]{
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

[selector="paginattion_area"] a {
  text-decoration: none;
  display: inline-block;
  padding: 8px 16px;
  color: #444444;
  background-color: #f0f0f0;
}

[selector="paginattion_area"] a[selector="previous"]:has(~ a[selector="next"]) {
  margin-right: 5px;
}

[selector="paginattion_area"] a:hover {
  background-color: #dadada;
  color: black;
}
[selector="paginattion_area"] a.disabled {
  cursor: not-allowed;
  color: #999999;
  background-color: #f6f6f6;
}

[selector="blog_page_tags"] [selector="single_tag"]{
  text-decoration: none;
  display: inline-block;
  padding: 6px 25px;
  background-color: #f0f0f0;
  border-radius: 15px;
  margin-bottom: 5px;
}
[selector="blog_page_tags"] [selector="single_tag"].selected{
  background: #e5e5e5;
  color: #333;
}
