<main class="my-5">
    <div id="make_token"> 
        <div class="r-col-flexbox">
            <div class="responsive-continer">

                <div class="rcontainer">
                    <h5 class="mb-4">Create Access Token</h5>
                    <form id="RENTMY_TOKEN_FORM">
                        <div class="mb-3">
                            <label for="exampleInputEmail1" class="form-label">Api Key</label>
                            <input type="text" name="api_key" class="form-control" id="exampleInputEmail1">
                        </div>
                        <div class="mb-3">
                            <label for="exampleInputPassword1" class="form-label">Secret Key</label>
                            <input type="text" name="api_secret" class="form-control" id="exampleInputPassword1">
                        </div>

                        <div id="ERROR_RESPONSE" hidden class="alert alert-danger" role="alert">
                            A simple danger alert—check it out!
                        </div>

                        <button type="submit" class="btn btn-primary">
                            Submit <span id="RRENTMY_LOADER" hidden class="loader"></span>
                        </button>
                    </form>

                </div>

                <div id="FORM_AREA" hidden class="rcontainer mt-5">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-4">Create Access Token</h5>
                        <div class="rcontrols">
                            <button class="rbutton" id="copy-btn">Copy to Clipboard</button>
                        </div>
                    </div>
                    <div id="json-display"></div>
                </div>
                <div class="notification" id="notification">Copied to clipboard!</div>
            </div> 
        </div> 
    </div>

    <style>
        .r-col-flexbox{
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column; 
        }
        .responsive-continer{
            width: 100%;
        } 
        .form-control.invalid {
            border-color: #dc3545;
            padding-right: calc(1.5em + .75rem);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }

        .rcontainer {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            position: relative;
        }

        .rcontrols {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 20px;
        }

        .rbutton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .rbutton:hover {
            background-color: #2980b9;
        }

        #json-display {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #2ecc71;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .key {
            color: #d35400;
        }

        .string {
            color: #27ae60;
        }

        .number {
            color: #2980b9;
        }

        .boolean {
            color: #8e44ad;
        }

        .null {
            color: #7f8c8d;
        }


        #RRENTMY_LOADER {
            width: 15px;
            height: 15px;
            border: 2px solid #FFF;
            border-bottom-color: transparent;
            border-radius: 50%;
            display: inline-block;
            box-sizing: border-box;
            animation: rotation 0.8s linear infinite;
        }

        @keyframes rotation {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>

    <script src="/js/header.js"> </script>

</main>