<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    
    <script src="/config.js"> </script> 
    <script CDN_SCRIPT src=""></script>
    <link CDN_CSS rel="stylesheet" href="">
    
</head>
<body class="container border">  
  <main class="my-5">

    <!-- In page cart widget -->
    <div class="RentMyWrapperInpageCartWidget RentMyWrapper">

        <div InsideContainer>
            <section id="InpageCartWidgetLauncher" class="closed" data-rentmyattr="InpageCartWidgetLauncher" >
                <div class="branded" id="launcher-icon">
                    <i class="branded las la-luggage-cart"></i>
                </div>
                <div class="Summary">
                    <div class="DateRange">
                        <span>
                            <strong data-rentmyattr="DateText" >08-22-24 09:00 AM - 08-22-24 09:00 AM</strong>
                        </span>
                    </div>
                    <hr>
                    <div class="SummaryText">
                        <span><strong data-rentmyattr="TotalQuantity">2 items</strong></span>
                        <span class="TotalAmount"><strong data-rentmyattr="TotalAmount">$7,998.00</strong></span>
                    </div>
                </div>
            </section> 

            <section id="Sidebar" data-rentmyattr="Sidebar">
                <div class="SidebarInner open">
                    <div class="SideBarHeader">
                        <button class="branded CloseSidebar" data-rentmyattr="SidebarCloseIcon">
                            <i class="fas fa-times"></i>
                        </button>
                    <div class="branded HeaderTitle">My booking</div>
                    <div class="branded" data-rentmyattr="RenatalDateArea">
                        <div class="DateRangeTextAndIcon" data-rentmyattr="ToggleDatePicker">
                            <div class="d-flex" data-rentmyattr="DateRange">
                                <span data-rentmyattr="DateText">08-22-2024, 09:00 AM</span>
                            </div>
                            <i class="las la-calendar cp" ></i>
                        </div>

                        <div class="RentMyDatePicker" id="RentMyDatePicker" data-rentmyattr="DatePicker">
                            <!-- Date picker will show here -->
                        </div>
                    </div>
                </div>
                
                    <div class="CartItemsArea active">
                        <ul class="CartItems">
                            <li data-rentmyattr="CartItem">
                                <div class="Item">
                                    <div data-rentmyattr="ImageArea">
                                        <img class="bq-product-image" src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/d4802cc4-4bb5-43f3-ab07-a1baf18054eb/large_photo.jpg">
                                    </div>
                                    <div class="CartLine">
                                        <div class="ItemName my-2" data-rentmyattr="ItemNameArea">Two-Stall Lux Restroom Trailer</div>
                                        <div class="d-flex justify-content-between align-items-center mt-2">
                                            <div class="QuantityArea" data-rentmyattr="IncrDecrArea">
                                                <button class="QuantityToggler" data-rentmyattr="DecrementBtn">-</button>
                                                <span class="Quantity" data-rentmyattr="QuantityText">1</span>
                                                <button class="QuantityToggler" data-rentmyattr="IncrementBtn">+</button>
                                            </div>
                                            <strong class="ItemPrice" data-rentmyattr="ItemPrice">$3,999.00</strong>
                                        </div>
                                    </div>
                                    
                                    <button class="RemoveProduct" data-rentmyattr="DeleteIconArea"><i class="fas fa-times"></i></button>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <div class="SidebarSummery" data-rentmyattr="RentMySummeryTable">
                            <div class="DeviderLine"></div>                          
                            <div class="Details">
                                <strong>Subtotal</strong>
                                <strong data-rentmyattr="SubtotalAmount">$7,998.00</strong>
                            </div>                        
                            <div class="ButtonGroup">
                                <a class="branded Button" href="#" data-rentmyattr="CartPageUrl">View cart</a>
                                <button class="branded Button" data-rentmyattr="ProceedCheckoutBtn">Checkout</button>
                            </div>
                        </div>
                    </div>

                </div>
            </section>
        </div>  

    </div>




  </main>
<script>

</script>
</body>
</html>