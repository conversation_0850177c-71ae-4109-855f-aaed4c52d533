<section class="rentmy-innerpage-banner">
    <div class="rentmy-innerpage-overlay">
        <div class="container">
            <div class="container-inner">
                <div class="container-inner-overlay">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="rentmy-innerpage-body">
                                <div class="rentmy-innerpage-body-inner">
                                    <h1>Product View</h1>
                                    <div class="rentmy-breadcrumbs">
                                        <ul>
                                            <li><a href="/">Home</a></li>
                                            <li>Product View</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<main class="my-5 container">

    <div id="wrapper" class="RentMyWrapperPackageDetails RentMyWrapper" data-RentMyData="">
        <div data-rentmyattr="RentmyPackageComponent">
            <!-- Content -->

            <section id="content">
                <div class="content-wrap">
                    <div class="container">
                        <div class="single-product">
                            <div>
                                <div class="RentMyProductPackageRow">
                                    <div class="RentMyProductDetilsImg">
                                        <div class="RentMyProductDetailsImgList">
                                            <ul data-rentmyattr="RentMyProductImages">
                                                <li class="ActiveImg">
                                                    <img src="" alt="" />
                                                </li>
                                                <li>
                                                    <img src="" alt="" />
                                                </li>
                                                <li>
                                                    <img src="" alt="" />
                                                </li>
                                                <li>
                                                    <img src="" alt="" />
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="RentMyProductDetailsImgShow">
                                            <img data-rentmyattr="RentMyProductImage" src="" alt="" />
                                        </div>
                                    </div>
                                    <div class="RentMyProductDetilsInfo" data-rentmyattr="RentMyProductDetilsInfo">
                                        <div class="product-payment-details">
                                            <h2 class="RentMyProductName" data-rentmyattr="RentMyProductName">{{ product_name }}</h2>
            
                                            <div class="RentMyBuyRentToggle" data-rentmyattr="RentMyBuyRentToggle">
                                                <label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch">
                                                    <input type="checkbox" id="BuyRentToggleSwitch" data-rentmyattr="BuyRentToggleSwitch" />
                                                    <div class="ToggleSwitchRound"></div>
                                                </label>
                                            </div>
            
                                            <h2 class="RentMyProductPrice" data-rentmyattr="RentMyProductPrice">{{ product_price_text }}</h2>
            
                                            <div class="RentMyRecurring" data-rentmyattr="RentMyRecurring">
                                                <h6 data-rentmyattr="RecurringTitle">Recurring Pricing</h6>
                                                <ul data-rentmyattr="RecurringList">
                                                    <li data-rentmyattr="RecurringItem">Recurring</li>
                                                </ul>
                                            </div>
            
                                            <div class="RentMyVariant" data-rentmyattr="RentMyVariant">
                                                <h6 data-rentmyattr="VariantTitle">Rent My Variant Sizes</h6>
                                                <ul data-rentmyattr="VariantList">
                                                    <li data-rentmyattr="VariantItem">Small</li>
                                                </ul>
                                            </div>
            
                                            <div class="RentMyProductOptions" data-rentmyattr="RentMyProductOptions">
                                                <div class="CustomFieldInner">
                                                    <h6 data-rentmyattr="ProductOptionsTitle" class="mt-3">Product Options</h6>
                                                    <ul data-rentmyattr="ProductOptionsItem">
                                                        <div data-rentmyattr="type-select">
                                                            <p data-rentmyattr="fieldLabel" class="m-0 mt-2"></p>
                                                            <select></select>
                                                        </div>
                                                        <div data-rentmyattr="type-button">
                                                            <p data-rentmyattr="fieldLabel" class="m-0 mt-2"></p>
                                                            <li data-rentmyattr="fieldValue"></li>
                                                        </div>
                                                        <div data-rentmyattr="type-radio">
                                                            <!-- <p data-rentmyattr="fieldLabel" class="m-0 mt-2"></p>
                                                            <li data-rentmyattr="fieldValue"></li> -->
                                                        </div>
                                                        <div data-rentmyattr="type-richtext">
                                                            <p data-rentmyattr="fieldLabel" class="m-0 mt-2"></p>
                                                            <li data-rentmyattr="fieldValue"></li>
                                                        </div>
                                                    </ul>
                                                </div>
                                            </div>
            
                                            <div class="RentMyRentalStartDate" data-rentmyattr="RentMyRentalStartDate">
                                                <div data-rentmyattr="usualDateRange">
                                                    <h6 data-rentmyattr="RentalStartDateTitle">Select Rental Start Date</h6>
                                                    <ul data-rentmyattr="RentalStartDateList">
                                                        <li data-rentmyattr="Today">Today</li>
                                                        <li data-rentmyattr="Tomorrow">Tomorrow</li>
                                                        <li data-rentmyattr="PickDate">Pick Date</li>
                                                    </ul>
                                                    <span data-rentmyattr="RentalStartDateSelectedLabel">Today 08:00 AM</span>
                                                </div>
                                            </div>
            
                                            <!-- For Exact Times -->
                                            <div class="RentMyBookingExactTimes" data-rentmyattr="RentMyBookingExactTimes">
                                                <div data-rentmyattr="TimeArea">
                                                    <h6 data-rentmyattr="TitleTag">Select Start time</h6>
                                                    <ul>
                                                        <li data-rentmyattr="exactTimeItem" data-rentmyattr="activeClass=timeActive">12:00 AM</li>
                                                    </ul>
                                                </div>
                                                <div class="TourNotAvailableMsg" data-rentmyattr="TourNotAvailableMessageArea">
                                                    This tour is not available on the date you selected. Please pick another date.
                                                </div>
                                            </div>
            
                                            <div class="RmRentalOption" data-rentmyattr="RentMyRentalDateRange">
                                                <h6 data-rentmyattr="RentalDateRangeTitle">Rental Date Range</h6>
                                                <ul data-rentmyattr="RentalDateRangeList">
                                                    <li class="" data-rentmyattr="RentalDateRangeItem" data-rentmyattr="activeClass=RmDaterangeActive">
                                                        <div class="RmRentalOptionDaytime" data-rentmyattr="PricePreText">1 day</div>
                                                        <div data-rentmyattr="Price">$10.00</div>
                                                    </li>
                                                </ul>
                                            </div>
            
                                            <div class="RentMyRentalDateRange mb-3" data-rentmyattr="RentMyRentalEndDate">
                                                <ul>
                                                    <li class="mt-0" data-rentmyattr="RentalEndDatePicker">
                                                        <span data-rentmyattr="rangePickerLabel">Pick Date Range</span>
                                                    </li>
                                                </ul>
                                                <span data-rentmyattr="RentalEndDateSelectedLabel">Today 09:00 AM</span>
                                            </div>
            
                                            <div class="RentMyExactSelectDuration" data-rentmyattr="RentMyExactSelectDuration">
                                                <h6 data-rentmyattr="RentMyExactSelectDurationTitle">Select Duration</h6>
                                                <ul data-rentmyattr="RentMyExactSelectDurationList">
                                                    <li class="ExactSelectDurationActive" data-rentmyattr="RentMyExactSelectDurationItem">Exact Select Duration</li>
                                                </ul>
                                            </div>
            
                                            <div class="RentMyExactSelectTime" data-rentmyattr="RentMyExactSelectTime">
                                                <h6 data-rentmyattr="RentMyExactSelectTimeTitle">Select Exact Start time</h6>
                                                <ul data-rentmyattr="RentMyExactSelectTimeList">
                                                    <li class="ExactSelectTimeActive" data-rentmyattr="RentMyExactSelectTimeItem">Extact Times</li>
                                                </ul>
                                            </div>
            
                                            <div class="RentMyDeliveryOptions" data-rentmyattr="RentMyDeliveryOptions">
                                                <h6 data-rentmyattr="DeliveryOptionsTitle">Delivery Options</h6>
                                                <ul data-rentmyattr="DeliveryOptionsList">
                                                    <li data-rentmyattr="DeliveryOptionsItem">Local Move</li>
                                                </ul>
                                            </div>
            
                                            <div class="RentMySelectLocation" data-rentmyattr="RentMySelectLocation">
                                                <h6 data-rentmyattr="SelectLocationTitle">Select Location</h6>
                                                <ul data-rentmyattr="SelectLocationList">
                                                    <li data-rentmyattr="SelectLocationItem">Default location</li>
                                                </ul>
                                            </div>
            
                                            <div class="QuantityContainer" data-rentmyattr="RentmyQuantityContainer">
                                                <label data-rentmyattr="QuantityContainerTitle">Quantity</label>
                                                <div class="QuantityBtn">
                                                    <button class="RentMyBtn" data-rentmyattr="QuantityDecrementBtn">-</button>
                                                    <input type="text" autocomplete="off" name="qty" class="InputQuantity" data-rentmyattr="NumberOfQuantity" />
                                                    <button class="RentMyBtn" data-rentmyattr="QuantityIncrementBtn">+</button>
                                                </div>
                                                <small class="info">
                                                    <span data-rentmyattr="RentmyAvailableLabel">Available:</span>
                                                    <span data-rentmyattr="RentmyAvailableQty" class="ms-1">17</span>
                                                </small>
                                            </div>
            
                                            <div class="RentMyCartBtnArea" data-rentmyattr="RentMyCartBtnArea">
                                                <button class="RentMyBtn RentMyAddCartBtn" data-rentmyattr="RentMyAddCartBtn">ADD TO CART</button>
                                                <button class="RentMyBtn RentMyAddWishlistBtn" data-rentmyattr="RentMyAddToWishlistBtn">Add To Wishlist</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="RentMyProductPackageArea" data-rentmyattr="RentMyProductPackageArea">
                                        <div class="RentMyProductPackageAreaInner" data-rentmyattr="RentMyProductPackageAreaInner">
                                            <h6 data-rentmyattr="RentMyProductPackageAreaTitle">Package includes</h6>
                                            <div data-rentmyattr="RentMyProductPackageContent"></div>
                                            <div class="PackageSingleProduct" data-rentmyattr="PackageSingleProduct">
                                                <div class="PackageProductName" data-rentmyattr="PackageProductName">
                                                    <h5 data-rentmyattr="PackageProductNameTitle">test product buy (2)</h5>
                                                </div>
                                                <div class="PakageProductVarient" data-rentmyattr="PakageProductVarient">
                                                    <div class="PakageProductVarientInner" data-rentmyattr="PakageProductVarientInner">
                                                        <select class="form-control" data-rentmyattr="PakageProductVarientInnerSelect">
                                                            <option data-rentmyattr="PakageProductVarientInnerOption" value="276077">size: Blue, color: red</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="RentMyProductDescription">
                                <h3 class="RentMyProductDesTitle">Product Description</h3>
                                <div class="RentMyProductDesBody" data-rentmyattr="RentMyProductDescription"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>



        </div>

        <!-- In page cart widget -->
        <div class="RentMyWrapperInpageCartWidget RentMyWrapper"></div> 

    </div><!-- #wrapper end -->


    <div class="RentMyRelatedProduct">
        <h3 class="RentMyRelatedProductTitle">Related Products</h3>
        <div class="RentMyRelatedProductBody">
            <div class="RentMyRow" data-rentmyattr="RentMyRelatedProducts">
                <div class="RentMyProductItem" data-rentmyattr="RentMyProductItem">
                    <div class="RentMyProductItemInner">
                        <div class="RentMyProductImg">
                            <a href="#" data-rentmyattr="RentMyProductImageUrl">
                                <img data-rentmyattr="RentMyProductImage" class="ProductImg" alt="" />
                            </a>
                            <div class="RentMyProductOverlay"> 
    
                                <div class="WishlistSingleItemOption" data-rentmyattr="WishListBtnArea">
                                    <button class="WishlistAddButton" data-rentmyattr="RentMyAddToWishListBtn"> <i class="la la-heart-o"></i> </button>              
                                    <a class="WishlistAddCartButton" data-rentmyattr="RentMyAddToCartBtn" data-rentmyattr2="overlay=true" href="#"> <i class="la la-shopping-cart"></i> </a>
                                </div>
                                
                            </div>
                        </div>
                        <div class="RentMyProductBody">
                            <h4 class="ProductName" data-rentmyattr="RentMyProductName"> Product_name </h4>
                            <h5 class="ProductPrice" data-rentmyattr="RentMyProductPrice"> product_price </h5>
                            <div class="ProductButton" data-rentmyattr="ProductButtons">
                                <a class="ProductDetailsBtn" href="#" data-rentmyattr="RentMyViewDetailsBtn">View Details</a>
                                <button class="ProductCartBtn" href="#" data-rentmyattr="RentMyAddToCartBtn">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>





    <!-- In page cart widget -->
    <div class="RentMyWrapperInpageCartWidget RentMyWrapper"></div>


</main>