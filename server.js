const fs = require('fs')
const path = require('path')
const express = require('express')
const config = require('./config')
const cors = require('cors'); 
const storeWiseMeta = require('./seo-meta')

// set env variables
require('dotenv').config(); 

// Check if the 'config' object and its 'env' property exist.
// This is a safety check to prevent errors if 'config' or 'config.env' is undefined.
if (config?.env) {
  // Check if a 'PORT' environment variable is set.
  // If it is, convert it to an integer and assign it to config.env.PORT.
  // This allows the application's port to be configured via environment variables.
  if (process.env?.PORT) {
    config.env.PORT = parseInt(process.env?.PORT);
  }
  // Check if an 'API_BASE_URL' environment variable is set.
  // If it is, assign its value to config.env.API_BASE_URL.
  // This is useful for setting the base URL for API calls.
  if (process.env?.API_BASE_URL) {
    config.env.API_BASE_URL = process.env?.API_BASE_URL;
  }
  // Check if a 'DOMAIN' environment variable is set.
  // If it is, assign its value to config.DOMAIN.
  // This could be used for setting the application's domain.
  if (process.env?.DOMAIN) {
    config.DOMAIN = process.env?.DOMAIN;
  }
  // Check if a 'STORE_ID' environment variable is set.
  // If it is, convert it to an integer and assign it to config.store_id.
  // This is likely for identifying a specific store.
  if (process.env?.STORE_ID) {
    config.store_id = parseInt(process.env?.STORE_ID);
  }
  // Check if a 'LOCATION_ID' environment variable is set.
  // If it is, convert it to an integer and assign it to config.locationId.
  // This is probably for identifying a specific location within a store or organization.
  if (process.env?.LOCATION_ID) {
    config.locationId = parseInt(process.env?.LOCATION_ID);
  }
  // Check if a 'STORE_NAME' environment variable is set.
  // If it is, assign its value to config.store_name.
  // This could be for displaying the store's name in the application.
  if (process.env?.STORE_NAME) {
    config.store_name = process.env?.STORE_NAME;
  }
  // Check if an 'ACCESS_TOKEN' environment variable is set.
  // If it is, assign its value to config.access_token.
  // This is typically used for authentication or authorization with external services.
  if (process.env?.ACCESS_TOKEN) {
    config.access_token = process.env?.ACCESS_TOKEN;
  }
}

// console.log('config', config);


const port = process.env?.PORT || config?.env?.PORT || 8080

const utils = require('./assets/js/utls')
const RentMySeo = require('./assets/js/rentmy')

const NodeCache = require('node-cache'); // Import node-cache
const CACHE_TIME_IN_SECONDS = config?.env?.CACHE_TIME_IN_SECONDS || -1; // 600s = 10 minutes
const cache = new NodeCache(); 
// const cache = {get:()=>null,set:()=>{}}


const app = express();
app.use(cors());
app.use(express.static(path.join(__dirname, 'assets')));

const filePath = (fileName) => (path.join(__dirname, `html-files/${fileName}`))
 

app.get('/config.js', (req, res) => {     
  let config_file_path = path.join(__dirname, 'config.js')
  fs.readFile(config_file_path, 'utf8', (err, data) => {
    data = data.replace('module.exports = RENTMY_GLOBAL', '')
    data = data.replace('ENV_DOMAIN', process.env?.DOMAIN || utils.reqUrl(req))
    // This code replaces a placeholder in data string with an API base URL,
    // prioritizing an environment variable for configuration and falling back to a hardcoded default if the environment variable is not found.
    data = data.replace('ENV_API_BASE_URL', process.env?.API_BASE_URL || 'https://clientapi.rentmy.co/api/')
    data = data.replace('ENV_STORE_ID', process.env?.STORE_ID)
    data = data.replace('ENV_LOCATION_ID', process.env?.LOCATION_ID)
    data = data.replace('ENV_STORE_NAME', process.env?.STORE_NAME)
    data = data.replace('ENV_ACCESS_TOKEN', process.env?.ACCESS_TOKEN)
    data = data.replace('ENV_PORT', process.env?.PORT)
    data = data.replace('ENV_ASSET_URL', process.env?.ASSET_URL)
    res.type('.js')
    res.send(data);
  });
});


app.get('/robots.txt', (req, res) => {
  res.sendFile(path.join(__dirname, 'assets', 'robots.txt'));
});


app.get('/config-settings.js', async(req, res) => {
  let config_file_path = path.join(__dirname, 'config.js')
  fs.readFile(config_file_path, 'utf8', async (err, data) => {  
    
    let scriptContents = 'console.warn("Loaded single site")'

    let IS_SINGLE_SITE = /IS_SINGLE_SITE\s+?=\s+?true/g.test(data)
    if(!IS_SINGLE_SITE){
      let storeName = utils.getSubdomain(utils.reqFullUrl(req)) || config?.SUBDOMAIN
      const RentMy = new RentMySeo(config, { cache }); 
      await RentMy.getSettings(storeName) 
      let { store_id, locationId, store_name, access_token } = RentMy

      scriptContents = `
        if(typeof RENTMY_GLOBAL !== 'undefined'){
          RENTMY_GLOBAL.store_id = "${store_id}"
          RENTMY_GLOBAL.store_name = "${store_name}"
          RENTMY_GLOBAL.access_token = "${access_token}"
          RENTMY_GLOBAL.locationId = "${locationId}"
        }
        console.warn('Wao ---- loaded store settings')
      `
    } 
    res.type('.js')
    res.send(scriptContents);

  });
   
});


console.log('[route] >>> /sitemap.xml');
app.get('/sitemap.xml',  (req, res)=>{ 

  if(req.query.clear === 'cache'){
    cache.flushAll() 
  }

  const RentMy = new RentMySeo(config, { cache });
  RentMy.getSitemapData(req, res, {cache})
})

// Token generate route
if(!config.routes) config.routes = {}
config.routes['makeToken'] = { title: 'Make Token', file: "/make-token.html", path: '/make-token' }
if(config.contents?.pages){
  if(!config.routes?.about) config.routes['about'] = { title: 'About Us', file: false, path: '/about' }
  if(!config.routes?.contact) config.routes['contact'] = { title: 'Contact Us', file: false, path: '/contact' }
  if(!config.routes?.term_and_condition) config.routes['term_and_condition'] = { title: 'Terms and Conditions', file: false, path: '/terms-and-conditions' }
  config.routes['rentmy_custom_pages'] = { title: 'Custom', file: false, path: '/page/{page_slug}' } 
}


let keys = Object.keys(config.routes);
keys.forEach(async (key) => {

  let route = config.routes[key];
  route.path = route.path.split('?')[0];
  if(!route.path.startsWith('/')) route.path = '/' + route.path
  route.path = route.path.replace(/\{/g, ":").replace(/\}/g, "").replace(':uuid', ':uid')


  console.log('[route] >>> ' + route.path);
  
  app.get(route.path, async (req, res) => {

    if (route.path !== '/make-token' && !config.access_token) {
      return res.redirect('/make-token');
    }

    const cache_key = `${config.store_id}_${utils.reqFullUrl(req)}`;
    
    if(req.query.clear === 'cache'){
      // console.log('cache flushAll()');
      cache.flushAll() 
    }

    if(req.query.clear === 'this-cache'){
      cache.del(cache_key);
    }
    
    // Check if response exists in cache
    const cachedContent = cache.get(cache_key);
    if (cachedContent) {
      console.log(`[cache] Hit for key: ${cache_key}`);
      return res.send(cachedContent);
    }
    let index_file_path = __dirname + '/index.html'
    fs.readFile(index_file_path, 'utf8', async (err, html) => { 

      const RentMy = new RentMySeo(config, { key, cache, storeWiseMeta });
      
      let file_path = filePath(route?.file || '');
      let has_dynamic_file = true
      if(route?.file && !fs.existsSync(file_path)){
        res.send(`"${file_path}", does not exist`)
        return
      } 
      if(route?.file === false){
        has_dynamic_file = false
        file_path = index_file_path
      } 
 
      html = await RentMy.setStoreLogo(html) || html 
      html = await RentMy.getNavigations(req, html, route) || html 
      
      fs.readFile(file_path, 'utf8', async (err2, HTMLPageContnets) => {
        
        if (err2) {
          console.error('Error reading file:', err2)
          res.status(500).send('Internal Server Error')
          return;
        }   
        
        let metaData = await RentMy.getMetaData(req, key, { route }) || {}
        
        html = utils.setCdnUrls(html, config, {key, route})
        HTMLPageContnets = await utils.modifyTheHTML(HTMLPageContnets, config, {key, route, req, RentMy})

        // rentmy_custom_pages 
        if(!has_dynamic_file) {
          let data = await RentMy.getRentmyPageContent(req, route) // with meta
          HTMLPageContnets = data?.contents || ''
          metaData = data 
        } 
        else if(has_dynamic_file && route.rentmy_page === true && req.params.page_slug){
          let data = await RentMy.getRentmyPageContent(req, route, { HTMLPageContnets } ) 
          HTMLPageContnets = data?.contents || '' 
          metaData = data 
        }
        else if(key === 'blog'){
          let data = await RentMy.getBlogPageContent(req, route, { HTMLPageContnets } ) 
          HTMLPageContnets = data?.contents || '' 
          metaData = data 
        }

        let html_with_seoMeta = utils.setMetaData(html, {
          RentMy,
          req,
          ...metaData, 
          key,
          route,
          storeName: config.store_name, 
          dynamic_contents: HTMLPageContnets,
          dynamic_contents_selector: 'body', 
        }); 

        let full_html = utils.setContents(html_with_seoMeta, HTMLPageContnets, '#dynamic_page_contents', {req, RentMy})

        cache.set(cache_key, full_html, route?.cache_time_in_seconds || CACHE_TIME_IN_SECONDS); // Cache for 600 seconds (10 minutes)

        res.send(full_html);
      })
      
    }); 
    
  });

})
 
app.get('/*',  (req, res)=>{
  if (!config.access_token) {
    return res.redirect('/make-token');
  } else {
    return res.redirect('/') 
  } 
})


  
app.on('error', (err) => {
  console.error('Server error:', err);
});


// try {
//   app.listen(port, () => console.log(`Server running at http://localhost:${port}`));
// } catch (e) {
//   console.error('Startup failed:', e.message);
// }


app.listen(port, '0.0.0.0', () => {
  console.log(`Server running at http://localhost:${port}`)
}).on('error', (err) => {
  console.error('Server failed:', err);
  process.exit(1); // Let PM2 restart it properly
});
