# Element selectors

``` 
#dynamic_page_contents
#in_page_dynamic_contents
#dynamic_page_title
#breadcrumb_title_from_page_slug

```

# Example command of PM2

``` 
pm2 start ecosystem.config.js
pm2 start ecosystem.config.js --env production
pm2 start ecosystem.config.js --env staging

pm2 restart ecosystem.config.js --env production
pm2 restart ecosystem.config.js --env staging

pm2 start server.js --name my-app

```



# About Branches

branch:: "clients/{any-store-name}"
 - don't push anything, this (clients/....) pattern of branches.

branch:: "develop"
 - branch is for any store with all features. We will create specific branch from here.

branch:: "feature/global-change", 
 - any branch never merge to this branch.
 - This branch is for global feature add/update. 
 - Any global change we'll added here then we will merge to "develop" and other necessary branches.