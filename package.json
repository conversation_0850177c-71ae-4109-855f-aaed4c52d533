{"name": "rentmy-embed", "version": "*******.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "pm2": "pm2 start ecosystem.config.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.7.9", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsdom": "^25.0.1", "moment": "^2.30.1", "node-cache": "^5.1.2", "nodemon": "^3.1.9"}, "devDependencies": {"pm2": "^6.0.5"}}